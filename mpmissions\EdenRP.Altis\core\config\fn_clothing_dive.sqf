//  File: fn_clothing_dive.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Master configuration file for Altis Diving Shop.
private["_filter"];
_filter = param [0,0,[0]];
//Classname, Custom Display name (use nil for Cfg->DisplayName, price

//Shop Title Name
ctrlSetText[3103,"Steve's Diving Shop"];

switch (_filter) do
{
	//Uniforms
	case 0:
	{
		[
			["U_B_Wetsuit",nil,2000]
		];
	};

	//Hats
	case 1:
	{
		[
		];
	};

	//Glasses
	case 2:
	{
		[
			["G_Diving",nil,500]
		];
	};

	//Vest
	case 3:
	{
		[
			["V_RebreatherB",nil,5000]
		];
	};

	//Backpacks
	case 4:
	{
		[
		];
	};
};