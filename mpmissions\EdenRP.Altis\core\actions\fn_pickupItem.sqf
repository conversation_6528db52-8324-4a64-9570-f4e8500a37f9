//  File: fn_pickupItem.sqf
//	Author: <PERSON> "<PERSON>" Boardwine

//	Description: Master handling for picking up an item.
private["_obj","_itemInfo","_itemName","_illegal","_diff","_items","_data","_pickupList","_dollarValue","_text","_UINS","_i"];
//Prevent people from rapidly spamming pickup
//if((time - eden_action_delay) < 2) exitWith {hint "You can't rapidly use action keys!"};
//Initial params passed, 1 - Object, 2 - Name of picked item, 3 - Number of said item
params [
	["_obj",ObjNull,[Obj<PERSON>ull]],
    ["_var","",[""]],
    ["_val",0,[0]]
];
 if(getPlayerUID (_obj getVariable["inUse", ObjNull]) != (getPlayerUID player)) exitWith{closeDialog 0;};

//Check if brief is still there
if(isNull _obj || isPlayer _obj) exitWith {closeDialog 0;};

//Player must be within 3 meters of the object
if(player distance _obj > 3) exitWith {closeDialog 0;};
((findDisplay 7125) displayCtrl 7127) ctrlEnable false;
((findDisplay 7125) displayCtrl 7128) ctrlEnable false;
//Internet check for duping prevention
[[player],"EDENS_fnc_internetCheck",false,false] spawn EDEN_fnc_MP;
eden_didServerRespond = false;
private _maxDelayTime = time + 5;
waitUntil{time > _maxDelayTime || eden_didServerRespond};
if(time > _maxDelayTime) exitWith {hint "Pickup failed, try again.";};

//Begin picking up object
_itemName = [([_var,0] call EDEN_fnc_varHandle)] call EDEN_fnc_varToStr;
_illegal = [_var,eden_illegal_items] call EDEN_fnc_index;

_items = _obj getVariable "O_droppedItem";

//If you're a cop and items are NOT illegal, OR if you're not a cop then pick it up.
if (playerSide isEqualTo west && _illegal == -1 || !(playerSide isEqualTo west)) then {
	if(_var isEqualTo "money") then {
		if(call eden_restrictions) exitWith {hint "You are under player restrictions and cannot perform this action! Contact an administrator if you feel this is an error.";};
		if(isNil {_val}) exitWith {};
		if(isNull _obj || player distance _obj > 3) exitWith {closeDialog 0;};
		if(!isNil {_val}) then {
			player playmove "AinvPknlMstpSlayWrflDnon";
			uiSleep 0.5;
			titleText[format[localize "STR_NOTF_PickedMoney",[_val] call EDEN_fnc_numberText],"PLAIN DOWN"];
			[
				["event","Picked up Cash"],
				["player",name player],
				["player_id",getPlayerUID player],
				["value",[_val] call EDEN_fnc_numberText],
				["location",getPosATL player]
			] call EDEN_fnc_logIt;
			//Remove the item from the array
			{
				if ((_x select 0) isEqualTo "money") then {
				    _items deleteAt _forEachIndex;
				};
				uiSleep (0.1);
			} forEach _items;
			_obj setVariable["O_droppedItem",_items,true];
			eden_cash = eden_cash + _val;
			eden_cache_cash = eden_cache_cash + _val;
			eden_action_delay = time;
		};
	} else {
		//Check if enough room to pick up
		_diff = [_var,_val,eden_carryWeight,eden_maxWeight] call EDEN_fnc_calWeightDiff;
		if(_diff <= 0) exitWith {hint localize "STR_NOTF_InvFull";};

		if(_diff != _val) then {
			if(([true,_var,_diff] call EDEN_fnc_handleInv)) then {
				player playmove "AinvPknlMstpSlayWrflDnon";
				uiSleep 0.5;

				//Subtract the taken number of items from the array
				{
					if ((_x select 0) isEqualTo _var) then {
					    _x set [1,_val - _diff];
					};
					uiSleep (0.1);
				} forEach _items;
				_obj setVariable["O_droppedItem",_items,true];
				titleText[format[localize "STR_NOTF_Picked",_diff,_itemName],"PLAIN DOWN"];
				[
					["event","Picked up Item"],
					["player",name player],
					["player_id",getPlayerUID player],
					["item",_itemName],["amount",_val],
					["location",getPosATL player]
				] call EDEN_fnc_logIt;
			};
		} else {
			if(([true,_var,_val] call EDEN_fnc_handleInv)) then
			{
				//waitUntil{isNull _obj};
				player playmove "AinvPknlMstpSlayWrflDnon";
				uiSleep 0.5;
				//Remove the item from the array
				{
					if ((_x select 0) isEqualTo _var) then {
					    _items deleteAt _forEachIndex;
					};
					uiSleep (0.1);
				} forEach _items;
				_obj setVariable["O_droppedItem",_items,true];
				titleText[format[localize "STR_NOTF_Picked",_diff,_itemName],"PLAIN DOWN"];
			};
		};
	};
} else {
	//Seize items if cop and items are illegal
	_value = 0;

	_index = [_var,eden_illegal_items] call EDEN_fnc_index;
	if(_index != -1) then {
		_value = _value + (_val * (((eden_illegal_items) select _index) select 1));
	};

	titleText[format[localize "STR_NOTF_PickedEvidence",_itemName,[_value] call EDEN_fnc_numberText],"PLAIN DOWN"];

	if(_value > 0) then {
		[player,_value,1,150] call EDEN_fnc_splitPay;
	};

	//Remove the item from the array
	{
		if ((_x select 0) isEqualTo _var) then {
			_items deleteAt _forEachIndex;
		};
		uiSleep (0.1);
	} forEach _items;
	_obj setVariable["O_droppedItem",_items,true];
	//waitUntil {isNull _obj};
	eden_action_delay = time;
};
//If there are no more items, delete the object
if !(count _items > 0) then {
	deleteVehicle _obj;
	closeDialog 0;
};

//Update the UI when object picked up
disableSerialization;
waitUntil{!isNull findDisplay 7125};
_pickupList = ((findDisplay 7125) displayCtrl 7126);
_data = _obj getVariable "O_droppedItem";
if !(isNull _obj) then {
	lbClear _pickupList;
	{
		if (_x select 0 isEqualTo "money") then {
			_itemName = "Money";
			_dollarValue = [_x select 1] call EDEN_fnc_numberText;
			_text = format["%1 ($%2)",_itemName, _dollarValue];
		} else {
			_itemName = [([_x select 0,0] call EDEN_fnc_varHandle)] call EDEN_fnc_varToStr;
			_text = format["%1 (%2)",_itemName, _x select 1];
		};
		_UINS = uiNamespace setVariable [_x select 0,_obj];
		_i = _pickupList lbAdd _text;
		_pickupList lbSetData [_i,_x select 0];
	} forEach _data;
};
eden_action_pickingUp = false;
((findDisplay 7125) displayCtrl 7127) ctrlEnable true;
((findDisplay 7125) displayCtrl 7128) ctrlEnable true;
