//  File: fn_dopeMagRefill.sqf
//	Author: trimo
//	Description: Refills magazines at dope crate

private ["_crateOwner", "_val"];
(_this select 3) params [
	["_crate", objNull, [objNull]],
	["_val", 0, [0]]
];

_crateOwner = _crate getVariable ["owner", ""];

private _playerObject = objNull;
{
	if(isPlayer _x && getPlayerUID _x isEqualTo _crateOwner) exitWith {
		_playerObject = _x;
	};
} forEach playableUnits;

if (eden_cash < _val && eden_atmcash < _val) exitWith {hint "You cannot afford to refill mags.";};

private _action = [
	format ["Are you sure you want to refill mags for $%1.", [_val] call EDEN_fnc_numberText],
	"Confirmation",
	"Yes",
	"No"
] call BIS_fnc_guiMessage;

if (_action) then {
	if (eden_cash >= _val) then {
		eden_cash = eden_cash - _val;
		eden_cache_cash = eden_cache_cash - _val;
		[] call EDEN_fnc_refillMags;
		[3,500,name player] remoteExec ["EDEN_fnc_payPlayer", _playerObject];

	} else {
		eden_atmcash = eden_atmcash - _val;
		eden_cache_atmcash = eden_cache_atmcash - _val;
		[] call EDEN_fnc_refillMags;
		[3,500,name player] remoteExec ["EDEN_fnc_payPlayer", _playerObject];
	};
};