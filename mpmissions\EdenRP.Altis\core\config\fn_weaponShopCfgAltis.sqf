#include "..\..\macro.h"
//  File: fn_weaponShopCfgAltis.sqf
//	Author: <PERSON> "<PERSON>" Boardwine

//	Description: Master configuration file for the weapon shops.

//	Return:
//	String: Close the menu
//	Array:
//	[Shop Name,
//	[ //Array of items to add to the store
//		[classname,Custom Name (set nil for default),price]
//	]]
params [["_shop","",[""]]];
if(_shop == "") exitWith {closeDialog 0}; //Bad shop type passed.

switch(_shop) do {
	case "cop_basic": {
		switch(true) do {
			case (playerSide != west): {"You not a cop!"};
			case (getPlayerUID player isEqualTo "76561198068537683" || getPlayerUID player isEqualTo "76561198239526280"): {
				["Ryan's Gustapo Killer Collection",
					[
						["FirstAidKit","First Aid Kit",10,[]],
						["ToolKit","Tool Kit",10,[]],
						["ItemGPS","GPS",10,[]],
						["ItemMap","Map",10,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",10,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10,[]],
						["Rangefinder",nil,10,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",10,[]],

						["SMG_03_TR_hex","European Concealed Carry",10,[["50Rnd_570x28_SMG_03","Parliment Pooper",10]]],
						["SMG_03_TR_black","Tazer P90",10,[["50Rnd_570x28_SMG_03",nil,10]]],
						["hgun_P07_F","P07 9mm",10,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",10],["16Rnd_9x21_Mag",nil,10]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",10,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",10]]],
						["hgun_Pistol_heavy_01_green_F","4-five Pistol",10,[["11Rnd_45ACP_Mag",".45 11rnd mag",10]]],
						["arifle_SDAR_F","SDAR 5.56 mm",10,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",10]]],
						["arifle_MX_Black_F","MX 6.5 mm",10,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",10],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",10]]],
						["arifle_SPAR_01_blk_F",nil,10,[["30Rnd_556x45_Stanag",nil,10],["30Rnd_556x45_Stanag_Tracer_Red",nil,10]]],
						["arifle_MSBS65_black_F",nil,10,[["30Rnd_65x39_caseless_msbs_mag",nil,10],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,10]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",10,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",10],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",10]]],
						["arifle_MSBS65_GL_black_F",nil,10,[["30Rnd_65x39_caseless_msbs_mag",nil,10],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,10]]],
						["arifle_MXM_Black_F","MXM 6.5mm",10,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",10],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",10]]],
						["arifle_MSBS65_Mark_black_F",nil,10,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_SW_Black_F","MX-SW 6.5mm",10,[["100Rnd_65x39_caseless_black_mag","6.5mm 100Rnd STANAG Mag",10],["100Rnd_65x39_caseless_black_mag_tracer","6.5mm 100Rnd Tracer Mag",10]]],
						["srifle_DMR_03_F","Mk1 7.62mm",10,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",10]]],
						["srifle_DMR_02_F","MAR-10 .338",10,[["10Rnd_338_Mag",".338 LM 10 Rnd Mag",10]]],
						["arifle_SPAR_01_GL_blk_F",nil,10,[["30Rnd_556x45_Stanag",nil,10],["30Rnd_556x45_Stanag_Tracer_Red",nil,10]]],
						["arifle_SPAR_02_blk_F",nil,10,[["150Rnd_556x45_Drum_Mag_F",nil,10],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,10]]],
						["arifle_SPAR_03_blk_F",nil,110000,[["20Rnd_762x51_Mag",500]]],
						["arifle_ARX_blk_F",nil,10,[["30Rnd_65x39_caseless_green",nil,10],["30Rnd_65x39_caseless_green_mag_Tracer",nil,10]]],
						["srifle_DMR_07_blk_F",nil,10,[["20Rnd_650x39_Cased_Mag_F",nil,10]]],
						["arifle_AK12U_F",nil,10,[["30Rnd_762x39_AK12_Mag_F",nil,10],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,10]]],
						["arifle_AK12_F",nil,10,[["30Rnd_762x39_AK12_Mag_F",nil,10],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,10]]],
						["arifle_AK12_GL_F",nil,120000,[["30Rnd_762x39_AK12_Mag_F",nil,500],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_F",nil,10,[["75Rnd_762x39_AK12_Mag_F",nil,10],["75Rnd_762x39_AK12_Mag_Tracer_F",nil,10]]],
						["muzzle_snds_338_black",nil,10,[]],
						["muzzle_snds_H",nil,10,[]],
						["muzzle_snds_65_TI_blk_F","6.5 Stealth Sound Suppressor",10,[]],
						["muzzle_snds_B","7.62mm Suppressor",10,[]],
						["muzzle_snds_acp",".45 Supressor",10,[]],
						["muzzle_snds_L","9mm Supressor",10,[]],

						["optic_Aco_smg","ACO SMG Red",10,[]],
						["optic_ACO_grn_smg","ACO SMG Green",10,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",10,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",10,[]],
						["optic_ERCO_blk_F",nil,10,[]],
						["optic_Hamr","RCO Scope",10,[]],
						["optic_Arco","ARCO Scope",10,[]],
						["optic_Arco_blk_F","ARCO Scope",10,[]],
						["optic_Arco_AK_blk_F",nil,10,[]],
						["optic_MRCO","MRCO Scope",10,[]],
						["optic_ico_01_black_f",nil,10,[]],
						["optic_DMS",nil,10,[]],
						["optic_MRD_black",nil,10,[]],
						["acc_flashlight","Flashlight",10,[]],
						["acc_flashlight_pistol","Pistol Flashlight",10,[]],
						["acc_pointer_IR","IR Laser Pointer",10,[]],
						["bipod_01_F_blk","Bipod",10,[]],
						["HandGrenade_Stone","Flashbang",10,[]],
						["SmokeShell",nil,10,[]],
						["SmokeShellBlue",nil,10,[]],
						["1Rnd_SmokeOrange_Grenade_shell","Teargas",10,[]],
						["1Rnd_SmokeBlue_Grenade_shell",nil,10,[]],
						["3Rnd_SmokeOrange_Grenade_shell","3GL Teargas",10,[]],
						["3Rnd_SmokeBlue_Grenade_shell",nil,10,[]],
						["SmokeShellOrange","Teargas",10,[]]
					]
				];
			};
			case (__GETC__(life_coplevel) isEqualTo 1): {
				["Derputies Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",10000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],

						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["optic_Holosight_blk_F","Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F","Mk17 SMG Holosight Black",500,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["Rangefinder",nil,2000,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]]
					]
				];
			};

			case (__GETC__(life_coplevel) isEqualTo 2): {
				["Patrol Officer Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["NVGoggles_INDEP","Night Vision Goggles",1000,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["HandGrenade_Stone","Flashbang",1700,[]],

						["arifle_SPAR_01_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_MX_Black_F","MX 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",13000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],

						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Rangefinder",nil,2000,[]]
					]
				];
			};
			case (__GETC__(life_coplevel) isEqualTo 3): {
				["Corporal Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["HandGrenade_Stone","Flashbang",1700,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["1Rnd_SmokeOrange_Grenade_shell","Teargas",2000,[]],
						["1Rnd_SmokeBlue_Grenade_shell",nil,1000,[]],
						["3Rnd_SmokeOrange_Grenade_shell","3GL Teargas",8000,[]],
						["3Rnd_SmokeBlue_Grenade_shell",nil,4000,[]],
						["SmokeShellOrange","Teargas",2000,[]],

						["arifle_MXM_Black_F","MXM 6.5mm",85000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_ARX_blk_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_black_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_02_blk_F",nil,150000,[["150Rnd_556x45_Drum_Mag_F",nil,1000],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,1000]]],
						["arifle_SPAR_01_GL_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["SMG_03_TR_black",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["srifle_DMR_07_blk_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["arifle_MSBS65_GL_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_MSBS65_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_01_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_MX_Black_F","MX 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",13000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],

						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_02_F_blk","Bipod",10000,[]],
						["bipod_03_F_blk","Bipod",10000,[]],
						["bipod_01_F_blk","Bipod",10000,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["NVGoggles_INDEP","Night Vision Goggles",1000,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Rangefinder",nil,2000,[]]
					]
				];
			};
			case (__GETC__(life_coplevel) isEqualTo 4): {
				["Retired SrAPD Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["HandGrenade_Stone","Flashbang",1700,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["1Rnd_SmokeOrange_Grenade_shell","Teargas",2000,[]],
						["1Rnd_SmokeBlue_Grenade_shell",nil,1000,[]],
						["3Rnd_SmokeOrange_Grenade_shell","3GL Teargas",8000,[]],
						["3Rnd_SmokeBlue_Grenade_shell",nil,4000,[]],
						["SmokeShellOrange","Teargas",2000,[]],

						["srifle_DMR_02_F","MAR-10 .338",130000,[["10Rnd_338_Mag",".338 LM 10 Rnd Mag",500]]],
						["srifle_DMR_03_F","Mk1 7.62mm",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_EBR_ACO_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_SPAR_03_blk_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_AK12U_F",nil,105000,[["30Rnd_762x39_AK12_Mag_F",nil,500],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,500]]],
						["SMG_03_TR_black",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_MXM_Black_F","MXM 6.5mm",85000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_ARX_blk_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_black_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_02_blk_F",nil,150000,[["150Rnd_556x45_Drum_Mag_F",nil,1000],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,1000]]],
						["arifle_SPAR_01_GL_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["srifle_DMR_07_blk_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["arifle_MSBS65_GL_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_MSBS65_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_01_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_MX_Black_F","MX 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",13000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],

						["muzzle_snds_acp",".45 Suppressor",150000,[]],
				    ["muzzle_snds_L","9mm Suppressor",150000,[]],
						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_02_F_blk","Bipod",10000,[]],
						["bipod_03_F_blk","Bipod",10000,[]],
						["bipod_01_F_blk","Bipod",10000,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["NVGoggles_INDEP","Night Vision Goggles",1000,[]],
						["O_NVGoggles_grn_F","Night Vision Goggles",1000,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Rangefinder",nil,2000,[]]
					]
				];
			};
			case (__GETC__(life_coplevel) isEqualTo 5): {
				["Staff Sergeant Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["HandGrenade_Stone","Flashbang",1700,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["1Rnd_SmokeOrange_Grenade_shell","Teargas",2000,[]],
						["1Rnd_SmokeBlue_Grenade_shell",nil,1000,[]],
						["3Rnd_SmokeOrange_Grenade_shell","3GL Teargas",8000,[]],
						["3Rnd_SmokeBlue_Grenade_shell",nil,4000,[]],
						["SmokeShellOrange","Teargas",2000,[]],

						["srifle_DMR_02_F","MAR-10 .338",130000,[["10Rnd_338_Mag",".338 LM 10 Rnd Mag",500]]],
						["srifle_DMR_03_F","Mk1 7.62mm",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_EBR_ACO_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_SPAR_03_blk_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_AK12U_F",nil,105000,[["30Rnd_762x39_AK12_Mag_F",nil,500],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,500]]],
						["SMG_03_TR_black",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_MXM_Black_F","MXM 6.5mm",85000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_ARX_blk_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_black_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_02_blk_F",nil,150000,[["150Rnd_556x45_Drum_Mag_F",nil,1000],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,1000]]],
						["arifle_SPAR_01_GL_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["srifle_DMR_07_blk_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["arifle_MSBS65_GL_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_MSBS65_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_01_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_MX_Black_F","MX 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",13000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],

						["muzzle_snds_acp",".45 Suppressor",150000,[]],
						["muzzle_snds_L","9mm Suppressor",150000,[]],
						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_02_F_blk","Bipod",10000,[]],
						["bipod_03_F_blk","Bipod",10000,[]],
						["bipod_01_F_blk","Bipod",10000,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["NVGoggles_INDEP","Night Vision Goggles",1000,[]],
						["O_NVGoggles_grn_F","Night Vision Goggles",1000,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Rangefinder",nil,2000,[]]
					]
				];
			};
			case (__GETC__(life_coplevel) isEqualTo 6): {
				["Sergeant Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["HandGrenade_Stone","Flashbang",1700,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["1Rnd_SmokeOrange_Grenade_shell","Teargas",2000,[]],
						["1Rnd_SmokeBlue_Grenade_shell",nil,1000,[]],
						["3Rnd_SmokeOrange_Grenade_shell","3GL Teargas",8000,[]],
						["3Rnd_SmokeBlue_Grenade_shell",nil,4000,[]],
						["SmokeShellOrange","Teargas",2000,[]],


						["srifle_DMR_02_F","MAR-10 .338",130000,[["10Rnd_338_Mag",".338 LM 10 Rnd Mag",500]]],
						["srifle_DMR_03_F","Mk1 7.62mm",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_EBR_ACO_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_SPAR_03_blk_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_AK12U_F",nil,105000,[["30Rnd_762x39_AK12_Mag_F",nil,500],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,500]]],
						["SMG_03_TR_black",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_MXM_Black_F","MXM 6.5mm",85000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_ARX_blk_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_black_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_02_blk_F",nil,150000,[["150Rnd_556x45_Drum_Mag_F",nil,1000],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,1000]]],
						["arifle_SPAR_01_GL_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["srifle_DMR_07_blk_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["arifle_MSBS65_GL_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_MSBS65_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_01_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_MX_Black_F","MX 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",13000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_Signal_F","Starter pistol",5000,[["6Rnd_GreenSignal_F","6Rnd Green Signal",500],["6Rnd_RedSignal_F","6Rnd Red Signal",500]]],

						["muzzle_snds_acp",".45 Suppressor",150000,[]],
				    ["muzzle_snds_L","9mm Suppressor",150000,[]],
						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_02_F_blk","Bipod",10000,[]],
						["bipod_03_F_blk","Bipod",10000,[]],
						["bipod_01_F_blk","Bipod",10000,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["NVGoggles_INDEP","Night Vision Goggles",1000,[]],
						["O_NVGoggles_grn_F","Night Vision Goggles",1000,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Rangefinder",nil,2000,[]]
					]
				];
			};
			case (__GETC__(life_coplevel) isEqualTo 7): {
				["Lieutenant Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["HandGrenade_Stone","Flashbang",1700,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["1Rnd_SmokeOrange_Grenade_shell","Teargas",2000,[]],
						["1Rnd_SmokeBlue_Grenade_shell",nil,1000,[]],
						["3Rnd_SmokeOrange_Grenade_shell","3GL Teargas",8000,[]],
						["3Rnd_SmokeBlue_Grenade_shell",nil,4000,[]],
						["SmokeShellOrange","Teargas",2000,[]],


						["srifle_DMR_02_F","MAR-10 .338",130000,[["10Rnd_338_Mag",".338 LM 10 Rnd Mag",500]]],
						["srifle_DMR_03_F","Mk1 7.62mm",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_MX_SW_Black_F","MX-SW 6.5mm",120000,[["100Rnd_65x39_caseless_black_mag","6.5mm 100Rnd STANAG Mag",500],["100Rnd_65x39_caseless_black_mag_tracer","6.5mm 100Rnd Tracer Mag",500]]],
						["LMG_03_Vehicle_F",nil,160000,[["200Rnd_556x45_Box_Red_F",nil,500],["200Rnd_556x45_Box_Tracer_Red_F",nil,500]]],
						["srifle_EBR_ACO_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_SPAR_03_blk_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_AK12_GL_F",nil,120000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["arifle_AK12_F",nil,120000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["arifle_AK12U_F",nil,105000,[["30Rnd_762x39_AK12_Mag_F",nil,500],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,500]]],
						["SMG_03_TR_black",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_MXM_Black_F","MXM 6.5mm",85000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_ARX_blk_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_black_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_02_blk_F",nil,150000,[["150Rnd_556x45_Drum_Mag_F",nil,1000],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,1000]]],
						["arifle_SPAR_01_GL_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["srifle_DMR_07_blk_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["arifle_MSBS65_GL_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_MSBS65_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_01_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_MX_Black_F","MX 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",13000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_Signal_F","Starter pistol",5000,[["6Rnd_GreenSignal_F","6Rnd Green Signal",500],["6Rnd_RedSignal_F","6Rnd Red Signal",500]]],

						["muzzle_snds_acp",".45 Suppressor",150000,[]],
				    ["muzzle_snds_L","9mm Suppressor",150000,[]],
						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_02_F_blk","Bipod",10000,[]],
						["bipod_03_F_blk","Bipod",10000,[]],
						["bipod_01_F_blk","Bipod",10000,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["NVGoggles_INDEP","Night Vision Goggles",1000,[]],
						["O_NVGoggles_grn_F","Night Vision Goggles",1000,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Rangefinder",nil,2000,[]]
					]
				];
			};
			case ((__GETC__(life_coplevel) isEqualTo 8)): {
				["Staff Chief Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["HandGrenade_Stone","Flashbang",1700,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["1Rnd_SmokeOrange_Grenade_shell","Teargas",2000,[]],
						["1Rnd_SmokeBlue_Grenade_shell",nil,1000,[]],
						["3Rnd_SmokeOrange_Grenade_shell","3GL Teargas",8000,[]],
						["3Rnd_SmokeBlue_Grenade_shell",nil,4000,[]],
						["SmokeShellOrange","Teargas",2000,[]],

						["hgun_Pistol_Signal_F","Starter pistol",5000,[["6Rnd_GreenSignal_F","6Rnd Green Signal",500],["6Rnd_RedSignal_F","6Rnd Red Signal",500]]],
						["arifle_RPK12_F",nil,135000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["srifle_DMR_02_F","MAR-10 .338",130000,[["10Rnd_338_Mag",".338 LM 10 Rnd Mag",500]]],
						["srifle_DMR_03_F","Mk1 7.62mm",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_MX_SW_Black_F","MX-SW 6.5mm",120000,[["100Rnd_65x39_caseless_black_mag","6.5mm 100Rnd STANAG Mag",500],["100Rnd_65x39_caseless_black_mag_tracer","6.5mm 100Rnd Tracer Mag",500]]],
						["LMG_03_Vehicle_F",nil,160000,[["200Rnd_556x45_Box_Red_F",nil,500],["200Rnd_556x45_Box_Tracer_Red_F",nil,500]]],
						["srifle_EBR_ACO_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_SPAR_03_blk_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_AK12_GL_F",nil,120000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["arifle_AK12_F",nil,120000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["arifle_AK12U_F",nil,105000,[["30Rnd_762x39_AK12_Mag_F",nil,500],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,500]]],
						["SMG_03_TR_black",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_MXM_Black_F","MXM 6.5mm",85000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_ARX_blk_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_black_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_02_blk_F",nil,150000,[["150Rnd_556x45_Drum_Mag_F",nil,1000],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,1000]]],
						["arifle_SPAR_01_GL_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["srifle_DMR_07_blk_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["arifle_MSBS65_GL_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_MSBS65_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_01_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_MX_Black_F","MX 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",13000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_Signal_F","Starter pistol",5000,[["6Rnd_GreenSignal_F","6Rnd Green Signal",500],["6Rnd_RedSignal_F","6Rnd Red Signal",500]]],

						["muzzle_snds_acp",".45 Suppressor",150000,[]],
				    ["muzzle_snds_L","9mm Suppressor",150000,[]],
						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_02_F_blk","Bipod",10000,[]],
						["bipod_03_F_blk","Bipod",10000,[]],
						["bipod_01_F_blk","Bipod",10000,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["NVGoggles_INDEP","Night Vision Goggles",1000,[]],
						["O_NVGoggles_grn_F","Night Vision Goggles",1000,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Rangefinder",nil,2000,[]]
					]
				];
			};
			case ((__GETC__(life_coplevel) isEqualTo 9)): {
				["Dep. Chief Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["HandGrenade_Stone","Flashbang",1700,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["1Rnd_SmokeOrange_Grenade_shell","Teargas",2000,[]],
						["1Rnd_SmokeBlue_Grenade_shell",nil,1000,[]],
						["3Rnd_SmokeOrange_Grenade_shell","3GL Teargas",8000,[]],
						["3Rnd_SmokeBlue_Grenade_shell",nil,4000,[]],
						["SmokeShellOrange","Teargas",2000,[]],

						["arifle_RPK12_F",nil,135000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["srifle_DMR_02_F","MAR-10 .338",130000,[["10Rnd_338_Mag",".338 LM 10 Rnd Mag",500]]],
						["srifle_DMR_03_F","Mk1 7.62mm",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_MX_SW_Black_F","MX-SW 6.5mm",120000,[["100Rnd_65x39_caseless_black_mag","6.5mm 100Rnd STANAG Mag",500],["100Rnd_65x39_caseless_black_mag_tracer","6.5mm 100Rnd Tracer Mag",500]]],
						["LMG_03_Vehicle_F",nil,160000,[["200Rnd_556x45_Box_Red_F",nil,500],["200Rnd_556x45_Box_Tracer_Red_F",nil,500]]],
						["srifle_EBR_ACO_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_SPAR_03_blk_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_AK12_GL_F",nil,120000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["arifle_AK12_F",nil,120000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["arifle_AK12U_F",nil,105000,[["30Rnd_762x39_AK12_Mag_F",nil,500],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,500]]],
						["SMG_03_TR_black",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_MXM_Black_F","MXM 6.5mm",85000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_ARX_blk_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_black_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_02_blk_F",nil,150000,[["150Rnd_556x45_Drum_Mag_F",nil,1000],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,1000]]],
						["arifle_SPAR_01_GL_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["srifle_DMR_07_blk_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["arifle_MSBS65_GL_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_MSBS65_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_01_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_MX_Black_F","MX 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",13000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_Signal_F","Starter pistol",5000,[["6Rnd_GreenSignal_F","6Rnd Green Signal",500],["6Rnd_RedSignal_F","6Rnd Red Signal",500]]],

						["muzzle_snds_H",nil,250000,[]],
						["muzzle_snds_65_TI_blk_F","6.5 Stealth Sound Suppressor",250000,[]],
						["muzzle_snds_acp",".45 Suppressor",150000,[]],
				    ["muzzle_snds_L","9mm Suppressor",150000,[]],
						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_02_F_blk","Bipod",10000,[]],
						["bipod_03_F_blk","Bipod",10000,[]],
						["bipod_01_F_blk","Bipod",10000,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["NVGoggles_INDEP","Night Vision Goggles",1000,[]],
						["O_NVGoggles_grn_F","Night Vision Goggles",1000,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Rangefinder",nil,2000,[]]
					]
				];
			};
			case (__GETC__(life_coplevel) isEqualTo 10): { //GOAT
				["Chief Equipment Shop",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["HandGrenade_Stone","Flashbang",1700,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["1Rnd_SmokeOrange_Grenade_shell","Teargas",2000,[]],
						["1Rnd_SmokeBlue_Grenade_shell",nil,1000,[]],
						["3Rnd_SmokeOrange_Grenade_shell","3GL Teargas",8000,[]],
						["3Rnd_SmokeBlue_Grenade_shell",nil,4000,[]],
						["SmokeShellOrange","Teargas",2000,[]],

						["arifle_RPK12_F",nil,135000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["srifle_DMR_02_F","MAR-10 .338",130000,[["10Rnd_338_Mag",".338 LM 10 Rnd Mag",500]]],
						["srifle_DMR_03_F","Mk1 7.62mm",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_MX_SW_Black_F","MX-SW 6.5mm",120000,[["100Rnd_65x39_caseless_black_mag","6.5mm 100Rnd STANAG Mag",500],["100Rnd_65x39_caseless_black_mag_tracer","6.5mm 100Rnd Tracer Mag",500]]],
						["LMG_03_Vehicle_F",nil,160000,[["200Rnd_556x45_Box_Red_F",nil,500],["200Rnd_556x45_Box_Tracer_Red_F",nil,500]]],
						["srifle_EBR_ACO_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_SPAR_03_blk_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["arifle_AK12_GL_F",nil,120000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["arifle_AK12_F",nil,120000,[["75rnd_762x39_AK12_Mag_F",nil,30000],["75rnd_762x39_AK12_Mag_Tracer_F",nil,30000]]],
						["arifle_AK12U_F",nil,105000,[["30Rnd_762x39_AK12_Mag_F",nil,500],["30Rnd_762x39_AK12_Mag_Tracer_F",nil,500]]],
						["SMG_03_TR_black",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_MXM_Black_F","MXM 6.5mm",85000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_ARX_blk_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_black_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_02_blk_F",nil,150000,[["150Rnd_556x45_Drum_Mag_F",nil,1000],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,1000]]],
						["arifle_SPAR_01_GL_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["srifle_DMR_07_blk_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["arifle_MSBS65_GL_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_MSBS65_black_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_SPAR_01_blk_F",nil,50000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_MX_Black_F","MX 6.5 mm",65000,[["30Rnd_65x39_caseless_black_mag","6.5mm 30Rnd STANAG Mag",500],["30Rnd_65x39_caseless_black_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_02_ACO_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",13000,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",500]]],
						["hgun_Pistol_heavy_01_green_F",nil,10000,[["11Rnd_45ACP_Mag",nil,500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_Signal_F","Starter pistol",5000,[["6Rnd_GreenSignal_F","6Rnd Green Signal",500],["6Rnd_RedSignal_F","6Rnd Red Signal",500]]],

						["muzzle_snds_H",nil,250000,[]],
						["muzzle_snds_65_TI_blk_F","6.5 Stealth Sound Suppressor",250000,[]],
						["muzzle_snds_acp",".45 Suppressor",150000,[]],
						["muzzle_snds_L","9mm Suppressor",150000,[]],
						["optic_Aco_smg","ACO SMG Red",500,[]],
						["optic_ACO_grn_smg","ACO SMG Green",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_MRD_black","4-Five Sight",1000,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_02_F_blk","Bipod",10000,[]],
						["bipod_03_F_blk","Bipod",10000,[]],
						["bipod_01_F_blk","Bipod",10000,[]],
						["NVGoggles_OPFOR","Police Night Vision Goggles",1000,[]],
						["NVGoggles_INDEP","Night Vision Goggles",1000,[]],
						["O_NVGoggles_grn_F","Night Vision Goggles",1000,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemRadio","Radio",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Rangefinder",nil,2000,[]]
					]
				];
			};
		};
	};

	case "med_basic": {
		switch (true) do {
			case (playerSide != independent): {"You are not an EMS Medic"};
			case (eden_newsTeam): {"You cannot access this store while acting as a News Member!"};
			case ((getPlayerUID player) isEqualTo "76561198144351505"): {
				["Director's EMS Shop",
					[
						["hgun_Pistol_Signal_F","Starter Pistol",500,[["6Rnd_GreenSignal_F","6Rnd Green Signal",50]]],
						["ItemGPS",nil,100,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",10,[]],
						["Binocular",nil,150,[]],
						["Rangefinder",nil,300,[]],
						["ToolKit",nil,250,[]],
						["Medikit","Med Kit",150,[]],
						["Chemlight_yellow","Road Flare",150,[]],
						["NVGoggles_INDEP",nil,1200,[]],
						["NVGoggles_OPFOR",nil,1000,[]],
						["NVGogglesB_gry_F",nil,100000,[]],
						["SmokeShellYellow","LZ Marker",400,[]]
					]
				];
			};
			case (__GETC__(life_medicLevel) > 6): {
				["Coordinator EMS Shop",
					[
						["hgun_Pistol_Signal_F","Starter Pistol",500,[["6Rnd_GreenSignal_F","6Rnd Green Signal",50]]],
						["ItemGPS",nil,100,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",10,[]],
						["Binocular",nil,150,[]],
						["Rangefinder",nil,300,[]],
						["ToolKit",nil,250,[]],
						["Medikit","Med Kit",150,[]],
						["Chemlight_yellow","Road Flare",150,[]],
						["NVGoggles_INDEP",nil,1200,[]],
						["NVGoggles_OPFOR",nil,1000,[]],
						["NVGogglesB_gry_F",nil,100000,[]],
						["SmokeShellYellow","LZ Marker",400,[]]
					]
				];
			};
			case (__GETC__(life_medicLevel) > 5): {
				["Supervisor EMS Shop",
					[
						["hgun_Pistol_Signal_F","Starter Pistol",500,[["6Rnd_GreenSignal_F","6Rnd Green Signal",50]]],
						["ItemGPS",nil,100,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",10,[]],
						["Binocular",nil,150,[]],
						["Rangefinder",nil,300,[]],
						["ToolKit",nil,250,[]],
						["Medikit","Med Kit",150,[]],
						["Chemlight_yellow","Road Flare",150,[]],
						["NVGoggles_INDEP",nil,1200,[]],
						["NVGoggles_OPFOR",nil,1000,[]],
						["SmokeShellYellow","LZ Marker",400,[]]
					]
				];
			};
			case (__GETC__(life_medicLevel) > 4): {
				["Staff EMS Shop",
					[
						["hgun_Pistol_Signal_F","Starter Pistol",500,[["6Rnd_GreenSignal_F","6Rnd Green Signal",50]]],
						["ItemGPS",nil,100,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",10,[]],
						["Binocular",nil,150,[]],
						["Rangefinder",nil,300,[]],
						["ToolKit",nil,250,[]],
						["Medikit","Med Kit",150,[]],
						["Chemlight_yellow","Road Flare",150,[]],
						["NVGoggles_INDEP",nil,1200,[]],
						["NVGoggles_OPFOR",nil,1000,[]],
						["SmokeShellYellow","LZ Marker",400,[]]
					]
				];
			};
			default {
				["Hospital EMS Shop",
					[
						["ItemGPS",nil,100,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",10,[]],
						["Binocular",nil,150,[]],
						["Rangefinder",nil,300,[]],
						["ToolKit",nil,250,[]],
						["Medikit","Med Kit",150,[]],
						["Chemlight_yellow","Road Flare",150,[]],
						["NVGoggles_INDEP",nil,1200,[]],
						["SmokeShellYellow","LZ Marker",400,[]]
					]
				];
			};
		};
	};

	case "rebel": {
		switch(true) do {
			case (playerSide != civilian): {"You are not a civilian!"};
			case (eden_newsTeam): {"You cannot access this store while acting as a News Member!"};
			case (!license_civ_rebel): {"You don't have a Rebel training license!"};
			case (getPlayerUID player isEqualTo "76561198068537683"): {
				["Ryan's Hanukkah Rebel Shop",
					[
						["hgun_Rook40_F","Rook-40 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_P07_khk_F","P07 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_01_F",nil,3500,[["10Rnd_9x21_Mag",nil,400]]],
						["hgun_PDW2000_F","PDW2000 9 mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500]]],
						["SMG_03_TR_hex","Jewish Tricks",10,[["50Rnd_570x28_SMG_03",nil,10]]],
						["SMG_05_F",nil,11000,[["30Rnd_9x21_Mag_SMG_02",nil,500],["30Rnd_9x21_Mag_SMG_02_Tracer_Green",nil,500]]],
						["sgun_HunterShotgun_01_sawedoff_F",nil,4,[["2Rnd_12Gauge_Pellets",nil,1],["2Rnd_12Gauge_Slug",nil,3]]],
						["sgun_HunterShotgun_01_F",nil,5,[["2Rnd_12Gauge_Pellets",nil,1],["2Rnd_12Gauge_Slug",nil,3]]],
						["arifle_SDAR_F","SDAR 5.56 mm",500,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_03_TR_camo",nil,500,[["50Rnd_570x28_SMG_03",nil,500]]],
						["SMG_01_F","Vermin SMG .45 ACP",500,[["30Rnd_45ACP_Mag_SMG_01",".45 ACP 30Rnd Vermin Mag",500]]],
						["arifle_Mk20C_F","Mk20C 5.56 mm (C)",500,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_TRG21_F","TRG-21 5.56mm",500,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_Katiba_F","Katiba 6.5mm",500,[["30Rnd_65x39_caseless_green","6.5mm 30Rnd STANAG Mag",500]]],
						["arifle_MXC_F","MXC 6.5mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_SPAR_03_khk_F",nil,500,[["20Rnd_762x51_Mag",nil,500]]],
						["arifle_MX_F","MX 6.5 mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MX_khk_F","MX 6.5 mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MSBS65_camo_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_sand_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MXM_F","MXM 6.5 mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MXM_khk_F","MXM 6.5 mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_SPAR_01_khk_F","SPAR-16 5.56 mm",500,[["30Rnd_556x45_Stanag",nil,500]]],
						["arifle_SPAR_02_khk_F","SPAR-16S 5.56 mm",500,[["150Rnd_556x45_Drum_Mag_F",nil,500]]],
						["arifle_MSBS65_Mark_camo_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_sand_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_SW_F","MX-SW 6.5 mm",500,[["100Rnd_65x39_caseless_mag",nil,500], ["100Rnd_65x39_caseless_mag_Tracer",nil,500]]],
						["arifle_MX_SW_khk_F","MX-SW 6.5 mm",500,[["100Rnd_65x39_caseless_mag",nil,500], ["100Rnd_65x39_caseless_mag_Tracer",nil,500]]],
						["arifle_AK12_arid_F",nil,500,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_AK12U_arid_F",nil,500,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_arid_F",nil,500,[["75Rnd_762x39_Mag_F",nil,5],["75Rnd_762x39_Mag_Tracer_F",nil,5]]],
						["arifle_AK12_lush_F",nil,500,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,500]]],
						["arifle_AK12U_lush_F",nil,500,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_lush_F",nil,500,[["75Rnd_762x39_Mag_F",nil,5],["75Rnd_762x39_Mag_Tracer_F",nil,5]]],
						["arifle_AKM_F",nil,500,[["30Rnd_762x39_Mag_F",nil,500],["30Rnd_762x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_AKS_F",nil,500,[["30Rnd_545x39_Mag_F",nil,500],["30Rnd_545x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_CTAR_blk_F",nil,500,[["30Rnd_580x42_Mag_F",nil,500],["30Rnd_580x42_Mag_Tracer_F",nil,500]]],
						["arifle_CTARS_blk_F",nil,500,[["100Rnd_580x42_Mag_F",nil,500],["100Rnd_580x42_Mag_Tracer_F",nil,500]]],
						["arifle_ARX_ghex_F",nil,500,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_ARX_hex_F",nil,500,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["srifle_DMR_07_ghex_F",nil,500,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["srifle_DMR_07_hex_F",nil,500,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["LMG_03_F",nil,500,[["200Rnd_556x45_Box_F",nil,500]]],
						["srifle_DMR_03_khaki_F","MK-1 7.62mm Khaki",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_03_tan_F","MK-1 7.62mm Sand",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_03_woodland_F","MK-1 7.62mm Woodland",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_03_multicam_F","MK-1 7.62mm Multicam",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_01_F","Rahim 7.62mm",500,[["10Rnd_762x54_Mag","7.62mm 10Rnd Mag",500]]],
						["srifle_EBR_F","Mk18 ABR 7.62mm",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_camo_F",nil,500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_olive_F",nil,500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_hunter_F",nil,215000,[["20Rnd_762x51_Mag",nil,500],["10Rnd_762x51_Mag",nil,250]]],
						["LMG_Mk200_F","MK200 LMG",500,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",500]]],
						["LMG_Mk200_black_F",nil,500,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",500]]],
						["LMG_Zafir_F",nil,500,[["150Rnd_762x54_Box_Tracer",nil,500]]],
						["launch_Titan_F","Titan Launcher",500,[["Titan_AA","Titan Rocket",500]]],
						["launch_B_Titan_olive_F",nil,500,[["Titan_AA",nil,500]]],
						["launch_I_Titan_F",nil,500,[["Titan_AA",nil,500]]],

						["muzzle_snds_B","7.62mm Suppressor",0,[]],
						["muzzle_snds_L","9mm Suppressor",0,[]],
						["muzzle_snds_H","6.5mm Suppressor",0,[]],
						["muzzle_snds_M","5.56 Suppressor",50,[]],
						["muzzle_snds_58_blk_F","5.8 Stealth Sound Suppressor",50,[]],
						["muzzle_snds_65_TI_blk_F","6.5 Stealth Sound Suppressor",50,[]],
						["muzzle_snds_338_black",nil,50,[]],
						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["optic_ACO_grn","ACO (Green)",500,[]],
						["optic_Aco","ACO (Red)",500,[]],
						["optic_Holosight","MK17 Holosight",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_DMS",nil,500,[]],
						["optic_MRCO","MRCO Scope",500,[]],
						["optic_Hamr","RCO Scope",500,[]],
						["optic_Arco","ARCO Scope",500,[]],
						["optic_Arco_blk_F","ARCO Scope",500,[]],
						["optic_Arco_arid_F",nil,1000,[]],
						["optic_Arco_lush_F",nil,1000,[]],
						["optic_Arco_AK_arid_F",nil,1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_Arco_AK_lush_F",nil,1000,[]],
						["optic_ico_01_f",nil,1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_ico_01_camo_f",nil,1000,[]],
						["optic_ico_01_sand_f",nil,1000,[]],
						["optic_ERCO_blk_F",nil,500,[]],
						["optic_ERCO_khk_F",nil,500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_01_F_snd","Bipod (Sand)",500,[]],
						["bipod_01_F_khk","Bipod (Khaki) [NATO]",500,[]],

						["DemoCharge_Remote_Mag","Explosive Charge",500,[]],
						["SLAMDirectionalMine_Wire_Mag","M6 SLAM Mine",500,[]],
						["IEDUrbanSmall_Remote_Mag", "Small Urban Explosive",500,[]],
						["IEDUrbanBig_Remote_Mag", "Big Urban Explosive",500,[]],

						["SmokeShell",nil,500,[]],
						["SmokeShellRed",nil,500,[]],
						["SmokeShellGreen",nil,500,[]],
						["SmokeShellPurple",nil,500,[]],
						["HandGrenade",nil,500,[]],

						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemWatch","Watch",50,[]],
						["MineDetector","Mine Detector",500,[]],
						["Binocular","Binocular",500,[]],
						["Rangefinder",nil,500,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]],
						["NVGoggles_OPFOR",nil,1000,[]],
						["O_NVGoggles_grn_F",nil,1000,[]],
						["O_NVGoggles_hex_F",nil,1000,[]],
						["O_NVGoggles_ghex_F",nil,1000,[]],
						["O_NVGoggles_urb_F",nil,1000,[]]
					]
				];
			};

			case (__GETC__(life_adminlevel) isEqualTo 4): {
				["Senior Staff Rebel Market",
					[
						["hgun_Rook40_F","Rook-40 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_P07_khk_F","P07 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_01_F",nil,3500,[["10Rnd_9x21_Mag",nil,400]]],
						["hgun_PDW2000_F","PDW2000 9 mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500]]],
						["SMG_05_F",nil,11000,[["30Rnd_9x21_Mag_SMG_02",nil,500],["30Rnd_9x21_Mag_SMG_02_Tracer_Green",nil,500]]],
						["sgun_HunterShotgun_01_sawedoff_F",nil,4,[["2Rnd_12Gauge_Pellets",nil,1],["2Rnd_12Gauge_Slug",nil,3]]],
						["sgun_HunterShotgun_01_F",nil,69,[["2Rnd_12Gauge_Pellets",nil,69]]],
						["arifle_SDAR_F","SDAR 5.56 mm",500,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_03_TR_camo",nil,500,[["50Rnd_570x28_SMG_03",nil,500]]],
						["SMG_01_F","Vermin SMG .45 ACP",500,[["30Rnd_45ACP_Mag_SMG_01",".45 ACP 30Rnd Vermin Mag",500]]],
						["arifle_Mk20C_F","Mk20C 5.56 mm (C)",500,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_TRG21_F","TRG-21 5.56mm",500,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_MXC_F","MXC 6.5mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_Katiba_F","Katiba 6.5mm",500,[["30Rnd_65x39_caseless_green","6.5mm 30Rnd STANAG Mag",500]]],
						["arifle_SPAR_03_khk_F",nil,500,[["20Rnd_762x51_Mag",nil,500]]],
						["arifle_MX_F","MX 6.5 mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MX_khk_F","MX 6.5 mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MSBS65_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_camo_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_sand_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MXM_F","MXM 6.5 mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MXM_khk_F","MXM 6.5 mm",500,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_SPAR_01_khk_F","SPAR-16 5.56 mm",500,[["30Rnd_556x45_Stanag",nil,500]]],
						["arifle_SPAR_02_khk_F","SPAR-16S 5.56 mm",500,[["150Rnd_556x45_Drum_Mag_F",nil,500]]],
						["arifle_MSBS65_Mark_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_camo_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_sand_F",nil,500,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_SW_F","MX-SW 6.5 mm",500,[["100Rnd_65x39_caseless_mag",nil,500], ["100Rnd_65x39_caseless_mag_Tracer",nil,500]]],
						["arifle_MX_SW_khk_F","MX-SW 6.5 mm",500,[["100Rnd_65x39_caseless_mag",nil,500], ["100Rnd_65x39_caseless_mag_Tracer",nil,500]]],
						["arifle_AK12_arid_F",nil,500,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_AK12U_arid_F",nil,500,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_arid_F",nil,500,[["75Rnd_762x39_Mag_F",nil,5],["75Rnd_762x39_Mag_Tracer_F",nil,5]]],
						["arifle_AK12_lush_F",nil,500,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,500]]],
						["arifle_AK12U_lush_F",nil,500,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_lush_F",nil,500,[["75Rnd_762x39_Mag_F",nil,5],["75Rnd_762x39_Mag_Tracer_F",nil,5]]],
						["arifle_AKM_F",nil,500,[["30Rnd_762x39_Mag_F",nil,500],["30Rnd_762x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_AKS_F",nil,500,[["30Rnd_545x39_Mag_F",nil,500],["30Rnd_545x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_CTAR_blk_F",nil,500,[["30Rnd_580x42_Mag_F",nil,500],["30Rnd_580x42_Mag_Tracer_F",nil,500]]],
						["arifle_CTARS_blk_F",nil,500,[["100Rnd_580x42_Mag_F",nil,500],["100Rnd_580x42_Mag_Tracer_F",nil,500]]],
						["arifle_ARX_ghex_F",nil,500,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_ARX_hex_F",nil,500,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["srifle_DMR_07_ghex_F",nil,500,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["srifle_DMR_07_hex_F",nil,500,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["LMG_03_F",nil,500,[["200Rnd_556x45_Box_F",nil,500]]],
						["srifle_DMR_03_khaki_F","MK-1 7.62mm Khaki",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_02_camo_F",nil,1,[["10Rnd_338_Mag",nil,500]]],
						["srifle_DMR_04_Tan_F",nil,1,[["10Rnd_127x54_Mag",nil,500]]],
						["srifle_DMR_03_tan_F","MK-1 7.62mm Sand",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_03_woodland_F","MK-1 7.62mm Woodland",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_03_multicam_F","MK-1 7.62mm Multicam",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_01_F","Rahim 7.62mm",500,[["10Rnd_762x54_Mag","7.62mm 10Rnd Mag",500]]],
						["srifle_EBR_F","Mk18 ABR 7.62mm",500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_camo_F",nil,500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_olive_F",nil,500,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_hunter_F",nil,85000,[["20Rnd_762x51_Mag",nil,500],["10Rnd_762x51_Mag",nil,250]]],
						["LMG_Mk200_F","MK200 LMG",500,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",500]]],
						["LMG_Mk200_black_F",nil,500,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",500]]],
						["LMG_Zafir_F",nil,500,[["150Rnd_762x54_Box_Tracer",nil,500]]],
						["launch_Titan_F","Titan Launcher",500,[["Titan_AA","Titan Rocket",500]]],
						["launch_B_Titan_olive_F",nil,500,[["Titan_AA",nil,500]]],
						["launch_I_Titan_F",nil,500,[["Titan_AA",nil,500]]],
						["launch_RPG7_F",nil,500,[["RPG7_F",nil,500]]],

						["muzzle_snds_B","7.62mm Suppressor",0,[]],
						["muzzle_snds_L","9mm Suppressor",0,[]],
						["muzzle_snds_H","6.5mm Suppressor",0,[]],
						["muzzle_snds_M","5.56 Suppressor",50,[]],
						["muzzle_snds_58_blk_F","5.8 Stealth Sound Suppressor",50,[]],
						["muzzle_snds_65_TI_blk_F","6.5 Stealth Sound Suppressor",50,[]],
						["muzzle_snds_338_black",nil,50,[]],
						["muzzle_snds_570","5.7mm Suppressor",30,[]],
						["muzzle_snds_acp",".45mm Suppressor",50,[]],

						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["optic_DMS",nil,500,[]],
						["optic_ACO_grn","ACO (Green)",500,[]],
						["optic_Aco","ACO (Red)",500,[]],
						["optic_Holosight","MK17 Holosight",500,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_MRCO","MRCO Scope",500,[]],
						["optic_Hamr","RCO Scope",500,[]],
						["optic_Arco","ARCO Scope",500,[]],
						["optic_Arco_blk_F","ARCO Scope",500,[]],
						["optic_Arco_arid_F",nil,1000,[]],
						["optic_Arco_lush_F",nil,1000,[]],
						["optic_Arco_AK_arid_F",nil,1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_Arco_AK_lush_F",nil,1000,[]],
						["optic_ico_01_f",nil,1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_ico_01_camo_f",nil,1000,[]],
						["optic_ico_01_sand_f",nil,1000,[]],
						["optic_ERCO_blk_F",nil,500,[]],
						["optic_ERCO_khk_F",nil,500,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_01_F_snd","Bipod (Sand)",500,[]],
						["bipod_01_F_khk","Bipod (Khaki) [NATO]",500,[]],

						["DemoCharge_Remote_Mag","Explosive Charge",500,[]],
						["SLAMDirectionalMine_Wire_Mag","M6 SLAM Mine",500,[]],
						["IEDUrbanSmall_Remote_Mag", "Small Urban Explosive",500,[]],
						["IEDUrbanBig_Remote_Mag", "Big Urban Explosive",500,[]],

						["SmokeShell",nil,500,[]],
						["SmokeShellRed",nil,500,[]],
						["SmokeShellGreen",nil,500,[]],
						["SmokeShellPurple",nil,500,[]],
						["HandGrenade",nil,500,[]],

						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemWatch","Watch",50,[]],
						["MineDetector","Mine Detector",500,[]],
						["Binocular","Binocular",500,[]],
						["Rangefinder",nil,500,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]],
						["NVGogglesB_gry_F",nil,1000,[]],
						["NVGoggles_OPFOR",nil,1000,[]],
						["O_NVGoggles_grn_F",nil,1000,[]],
						["O_NVGoggles_hex_F",nil,1000,[]],
						["O_NVGoggles_ghex_F",nil,1000,[]],
						["O_NVGoggles_urb_F",nil,1000,[]]
					]
				];
			};


			case ((__GETC__(eden_donator) >= 30) && (__GETC__(eden_donator) < 50)): {
				["Rebel Weapon Shop",
					[
						["hgun_Rook40_F","Rook-40 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_P07_khk_F","P07 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_01_F",nil,3500,[["10Rnd_9x21_Mag",nil,400]]],
						["hgun_PDW2000_F","PDW2000 9 mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500]]],
						["SMG_05_F",nil,11000,[["30Rnd_9x21_Mag_SMG_02",nil,500],["30Rnd_9x21_Mag_SMG_02_Tracer_Green",nil,500]]],
						["sgun_HunterShotgun_01_sawedoff_F",nil,45000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_01_F","Vermin SMG .45 ACP",40000,[["30Rnd_45ACP_Mag_SMG_01",".45 ACP 30Rnd Vermin Mag",500]]],
						["arifle_Mk20C_F","Mk20C 5.56 mm (C)",40000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_TRG21_F","TRG-21 5.56mm",40000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_MXC_F","MXC 6.5mm",50000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["SMG_03C_TR_camo",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_Katiba_F","Katiba 6.5mm",65000,[["30Rnd_65x39_caseless_green","6.5mm 30Rnd STANAG Mag",500]]],
						["arifle_SPAR_03_khk_F",nil,110000,[["20Rnd_762x51_Mag",nil,500]]],
						["arifle_MX_F","MX 6.5 mm",60000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MX_khk_F","MX 6.5 mm",60000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MSBS65_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_camo_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_sand_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MXM_F","MXM 6.5 mm",70000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MXM_khk_F","MXM 6.5 mm",70000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_SPAR_01_khk_F","SPAR-16 5.56 mm",70000,[["30Rnd_556x45_Stanag",nil,500]]],
						["arifle_SPAR_02_khk_F","SPAR-16S 5.56 mm",150000,[["150Rnd_556x45_Drum_Mag_F",nil,2000]]],
						["arifle_MSBS65_Mark_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_camo_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_sand_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_SW_F","MX-SW 6.5 mm",185000,[["100Rnd_65x39_caseless_mag",nil,4000], ["100Rnd_65x39_caseless_mag_Tracer",nil,4000]]],
						["arifle_MX_SW_khk_F","MX-SW 6.5 mm",185000,[["100Rnd_65x39_caseless_mag",nil,4000], ["100Rnd_65x39_caseless_mag_Tracer",nil,4000]]],
						["arifle_AK12_arid_F",nil,120000,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_AK12U_arid_F",nil,105000,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_arid_F",nil,160000,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["75Rnd_762x39_Mag_Tracer_F",nil,15000]]],
						["arifle_AK12_lush_F",nil,120000,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,500]]],
						["arifle_AK12U_lush_F",nil,105000,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_lush_F",nil,160000,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["75Rnd_762x39_Mag_Tracer_F",nil,15000]]],
						["arifle_AKM_F",nil,60000,[["30Rnd_762x39_Mag_F",nil,500],["30Rnd_762x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_AKS_F",nil,50000,[["30Rnd_545x39_Mag_F",nil,500],["30Rnd_545x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_CTAR_blk_F",nil,55000,[["30Rnd_580x42_Mag_F",nil,500],["30Rnd_580x42_Mag_Tracer_F",nil,500]]],
						["arifle_CTARS_blk_F",nil,70000,[["100Rnd_580x42_Mag_F",nil,3000],["100Rnd_580x42_Mag_Tracer_F",nil,3000]]],
						["arifle_ARX_ghex_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_ARX_hex_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["srifle_DMR_07_ghex_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["srifle_DMR_07_hex_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["LMG_03_F",nil,160000,[["200Rnd_556x45_Box_F",nil,5000]]],
						["srifle_DMR_03_khaki_F","MK-1 7.62mm Khaki",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_03_tan_F","MK-1 7.62mm Sand",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_01_F","Rahim 7.62mm",90000,[["10Rnd_762x54_Mag","7.62mm 10Rnd Mag",500]]],
						["srifle_EBR_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_camo_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_olive_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_hunter_F",nil,85000,[["20Rnd_762x51_Mag",nil,500],["10Rnd_762x51_Mag",nil,250]]],
						["LMG_Mk200_F","MK200 LMG",215000,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",5000]]],
						["LMG_Mk200_black_F",nil,215000,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",5000]]],
						["launch_Titan_F","Titan Launcher",400000,[["Titan_AA","Titan Rocket",150000]]],
						["launch_B_Titan_olive_F",nil,400000,[["Titan_AA",nil,150000]]],
						["launch_I_Titan_F",nil,400000,[["Titan_AA",nil,150000]]],

						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]],
						["optic_Holosight","MK17 Holosight",1000,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_arid_F",nil,1000,[]],
						["optic_Arco_lush_F",nil,1000,[]],
						["optic_Arco_AK_arid_F",nil,1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_Arco_AK_lush_F",nil,1000,[]],
						["optic_ico_01_f",nil,1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_ico_01_camo_f",nil,1000,[]],
						["optic_ico_01_sand_f",nil,1000,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_ERCO_khk_F",nil,1000,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_01_F_snd","Bipod (Sand)",10000,[]],
						["bipod_01_F_khk","Bipod (Khaki) [NATO]",10000,[]],

						["DemoCharge_Remote_Mag","Explosive Charge",190000,[]],
						["SLAMDirectionalMine_Wire_Mag","M6 SLAM Mine",200000,[]],
						["IEDUrbanSmall_Remote_Mag", "Small Urban Explosive",250000,[]],
						["IEDUrbanBig_Remote_Mag", "Big Urban Explosive",450000,[]],

						["SmokeShell",nil,1000,[]],
						["SmokeShellRed",nil,1000,[]],
						["SmokeShellGreen",nil,1000,[]],
						["SmokeShellPurple",nil,1000,[]],
						["HandGrenade",nil,50000,[]],

						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemWatch","Watch",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Binocular","Binocular",1000,[]],
						["Rangefinder",nil,2000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]],
						["O_NVGoggles_grn_F",nil,1000,[]],
						["O_NVGoggles_hex_F",nil,1000,[]],
						["O_NVGoggles_ghex_F",nil,1000,[]],
						["O_NVGoggles_urb_F",nil,1000,[]]
					]
				];
			};
			case (__GETC__(eden_donator) >= 50): {
				["Rebel Weapon Shop",
					[
						["hgun_Rook40_F","Rook-40 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_P07_khk_F","P07 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_01_F",nil,3500,[["10Rnd_9x21_Mag",nil,400]]],
						["hgun_PDW2000_F","PDW2000 9 mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500]]],
						["SMG_05_F",nil,11000,[["30Rnd_9x21_Mag_SMG_02",nil,500],["30Rnd_9x21_Mag_SMG_02_Tracer_Green",nil,500]]],
						["sgun_HunterShotgun_01_sawedoff_F",nil,45000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_01_F","Vermin SMG .45 ACP",40000,[["30Rnd_45ACP_Mag_SMG_01",".45 ACP 30Rnd Vermin Mag",500]]],
						["arifle_Mk20C_F","Mk20C 5.56 mm (C)",40000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_TRG21_F","TRG-21 5.56mm",40000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_MXC_F","MXC 6.5mm",50000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["SMG_03C_TR_camo",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_Katiba_F","Katiba 6.5mm",65000,[["30Rnd_65x39_caseless_green","6.5mm 30Rnd STANAG Mag",500]]],
						["arifle_SPAR_03_khk_F",nil,110000,[["20Rnd_762x51_Mag",nil,500]]],
						["arifle_MX_F","MX 6.5 mm",60000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MX_khk_F","MX 6.5 mm",60000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MSBS65_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_camo_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_sand_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MXM_F","MXM 6.5 mm",70000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MXM_khk_F","MXM 6.5 mm",70000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_SPAR_01_khk_F","SPAR-16 5.56 mm",70000,[["30Rnd_556x45_Stanag",nil,500]]],
						["arifle_SPAR_02_khk_F","SPAR-16S 5.56 mm",150000,[["150Rnd_556x45_Drum_Mag_F",nil,2000]]],
						["arifle_MSBS65_Mark_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_camo_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MSBS65_Mark_sand_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_SW_F","MX-SW 6.5 mm",185000,[["100Rnd_65x39_caseless_mag",nil,4000], ["100Rnd_65x39_caseless_mag_Tracer",nil,4000]]],
						["arifle_MX_SW_khk_F","MX-SW 6.5 mm",185000,[["100Rnd_65x39_caseless_mag",nil,4000], ["100Rnd_65x39_caseless_mag_Tracer",nil,4000]]],
						["arifle_AK12_arid_F",nil,120000,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_AK12U_arid_F",nil,105000,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_arid_F",nil,160000,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["75Rnd_762x39_Mag_Tracer_F",nil,15000]]],
						["arifle_AK12_lush_F",nil,120000,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,500]]],
						["arifle_AK12U_lush_F",nil,105000,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_lush_F",nil,160000,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,500],["75Rnd_762x39_Mag_Tracer_F",nil,15000]]],
						["arifle_AKM_F",nil,60000,[["30Rnd_762x39_Mag_F",nil,500],["30Rnd_762x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_AKS_F",nil,50000,[["30Rnd_545x39_Mag_F",nil,500],["30Rnd_545x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_CTAR_blk_F",nil,55000,[["30Rnd_580x42_Mag_F",nil,500],["30Rnd_580x42_Mag_Tracer_F",nil,500]]],
						["arifle_CTARS_blk_F",nil,70000,[["100Rnd_580x42_Mag_F",nil,3000],["100Rnd_580x42_Mag_Tracer_F",nil,3000]]],
						["arifle_ARX_ghex_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["arifle_ARX_hex_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["srifle_DMR_07_ghex_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["srifle_DMR_07_hex_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["LMG_03_F",nil,160000,[["200Rnd_556x45_Box_F",nil,5000]]],
						["srifle_DMR_03_khaki_F","MK-1 7.62mm Khaki",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_03_tan_F","MK-1 7.62mm Sand",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_03_woodland_F","MK-1 7.62mm Woodland",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_03_multicam_F","MK-1 7.62mm Multicam",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_01_F","Rahim 7.62mm",90000,[["10Rnd_762x54_Mag","7.62mm 10Rnd Mag",500]]],
						["srifle_EBR_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_camo_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_olive_F",nil,110000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_hunter_F",nil,85000,[["20Rnd_762x51_Mag",nil,500],["10Rnd_762x51_Mag",nil,250]]],
						["LMG_Mk200_F","MK200 LMG",215000,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",5000]]],
						["LMG_Mk200_black_F",nil,215000,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",5000]]],
						["launch_Titan_F","Titan Launcher",400000,[["Titan_AA","Titan Rocket",150000]]],
						["launch_B_Titan_olive_F",nil,400000,[["Titan_AA",nil,150000]]],
						["launch_I_Titan_F",nil,400000,[["Titan_AA",nil,150000]]],

						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]],
						["optic_Holosight","MK17 Holosight",1000,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_arid_F",nil,1000,[]],
						["optic_Arco_lush_F",nil,1000,[]],
						["optic_Arco_AK_arid_F",nil,1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_Arco_AK_lush_F",nil,1000,[]],
						["optic_ico_01_f",nil,1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_ico_01_camo_f",nil,1000,[]],
						["optic_ico_01_sand_f",nil,1000,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_ERCO_khk_F",nil,1000,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_01_F_snd","Bipod (Sand)",10000,[]],
						["bipod_01_F_khk","Bipod (Khaki) [NATO]",10000,[]],

						["DemoCharge_Remote_Mag","Explosive Charge",190000,[]],
						["SLAMDirectionalMine_Wire_Mag","M6 SLAM Mine",200000,[]],
						["IEDUrbanSmall_Remote_Mag", "Small Urban Explosive",250000,[]],
						["IEDUrbanBig_Remote_Mag", "Big Urban Explosive",450000,[]],

						["SmokeShell",nil,1000,[]],
						["SmokeShellRed",nil,1000,[]],
						["SmokeShellGreen",nil,1000,[]],
						["SmokeShellPurple",nil,1000,[]],
						["HandGrenade",nil,50000,[]],

						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemWatch","Watch",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Binocular","Binocular",1000,[]],
						["Rangefinder",nil,2000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]],
						["O_NVGoggles_grn_F",nil,1000,[]],
						["O_NVGoggles_hex_F",nil,1000,[]],
						["O_NVGoggles_ghex_F",nil,1000,[]],
						["O_NVGoggles_urb_F",nil,1000,[]]
					]
				];
			};
			default {
				["Rebel Weapon Shop",
					[
						["hgun_Rook40_F","Rook-40 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_P07_khk_F","P07 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_01_F",nil,3500,[["10Rnd_9x21_Mag",nil,400]]],
						["hgun_PDW2000_F","PDW2000 9 mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500]]],
						["SMG_05_F",nil,11000,[["30Rnd_9x21_Mag_SMG_02",nil,500],["30Rnd_9x21_Mag_SMG_02_Tracer_Green",nil,500]]],
						["sgun_HunterShotgun_01_sawedoff_F",nil,45000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["sgun_HunterShotgun_01_F",nil,55000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_01_F","Vermin SMG .45 ACP",40000,[["30Rnd_45ACP_Mag_SMG_01",".45 ACP 30Rnd Vermin Mag",500]]],
						["arifle_Mk20C_F","Mk20C 5.56 mm (C)",40000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_TRG21_F","TRG-21 5.56mm",40000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_MXC_F","MXC 6.5mm",50000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["SMG_03C_TR_camo",nil,70000,[["50Rnd_570x28_SMG_03",nil,500]]],
						["arifle_Katiba_F","Katiba 6.5mm",65000,[["30Rnd_65x39_caseless_green","6.5mm 30Rnd STANAG Mag",500]]],
						["arifle_SPAR_03_khk_F",nil,110000,[["20Rnd_762x51_Mag",nil,500]]],
						["arifle_MX_F","MX 6.5 mm",60000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MSBS65_F",nil,80000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MXM_F","MXM 6.5 mm",70000,[["30Rnd_65x39_caseless_mag",nil,500]]],
						["arifle_MSBS65_Mark_F",nil,90000,[["30Rnd_65x39_caseless_msbs_mag",nil,500],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,500]]],
						["arifle_MX_SW_F","MX-SW 6.5 mm",185000,[["100Rnd_65x39_caseless_mag",nil,4000], ["100Rnd_65x39_caseless_mag_Tracer",nil,4000]]],
						["arifle_AK12_arid_F",nil,120000,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_AK12U_arid_F",nil,105000,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,500]]],
						["arifle_RPK12_arid_F",nil,160000,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,500],["75Rnd_762x39_Mag_Tracer_F",nil,15000]]],
						["arifle_AKM_F",nil,60000,[["30Rnd_762x39_Mag_F",nil,500],["30Rnd_762x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_AKS_F",nil,50000,[["30Rnd_545x39_Mag_F",nil,500],["30Rnd_545x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_CTAR_blk_F",nil,55000,[["30Rnd_580x42_Mag_F",nil,500],["30Rnd_580x42_Mag_Tracer_F",nil,500]]],
						["arifle_CTARS_blk_F",nil,70000,[["100Rnd_580x42_Mag_F",nil,3000],["100Rnd_580x42_Mag_Tracer_F",nil,3000]]],
						["arifle_ARX_ghex_F",nil,80000,[["30Rnd_65x39_caseless_green",nil,500],["30Rnd_65x39_caseless_green_mag_Tracer",nil,500]]],
						["srifle_DMR_07_ghex_F",nil,75000,[["20Rnd_650x39_Cased_Mag_F",nil,500]]],
						["arifle_SPAR_01_khk_F","SPAR-16 5.56 mm",70000,[["30Rnd_556x45_Stanag",nil,500]]],
						["arifle_SPAR_02_khk_F","SPAR-16S 5.56 mm",150000,[["150Rnd_556x45_Drum_Mag_F",nil,2000]]],
						["LMG_03_F",nil,160000,[["200Rnd_556x45_Box_F",nil,5000]]],
						["srifle_DMR_03_khaki_F","Mk1 7.62mm",125000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_01_F","Rahim 7.62mm",90000,[["10Rnd_762x54_Mag","7.62mm 10Rnd Mag",500]]],
						["srifle_EBR_F","Mk18 ABR 7.62mm",100000,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",500]]],
						["srifle_DMR_06_camo_F",nil,110000,[["20Rnd_762x51_Mag",nil,500],["10Rnd_762x51_Mag",nil,250]]],
						["srifle_DMR_06_olive_F",nil,110000,[["20Rnd_762x51_Mag",nil,500],["10Rnd_762x51_Mag",nil,250]]],
						["srifle_DMR_06_hunter_F",nil,85000,[["20Rnd_762x51_Mag",nil,500],["10Rnd_762x51_Mag",nil,250]]],
						["LMG_Mk200_F","MK200 LMG",215000,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",5000]]],
						["LMG_Mk200_black_F",nil,215000,[["200Rnd_65x39_cased_Box","MK200 LMG 200Rnd Mag",5000]]],
						["launch_Titan_F","Titan Launcher",400000,[["Titan_AA","Titan Rocket",150000]]],

						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]],
						["optic_Holosight","MK17 Holosight",1000,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_Arco_AK_arid_F",nil,1000,[]],
						["optic_Arco_AK_blk_F",nil,1000,[]],
						["optic_Arco_AK_lush_F",nil,1000,[]],
						["optic_Arco_arid_F",nil,1000,[]],
						["optic_ico_01_f",nil,1000,[]],
						["optic_ico_01_black_f",nil,1000,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_ERCO_khk_F",nil,1000,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],
						["bipod_01_F_snd","Bipod (Sand)",10000,[]],

						["DemoCharge_Remote_Mag","Explosive Charge",190000,[]],
						["SLAMDirectionalMine_Wire_Mag","M6 SLAM Mine",200000,[]],
						["IEDUrbanSmall_Remote_Mag", "Small Urban Explosive",250000,[]],
						["IEDUrbanBig_Remote_Mag", "Big Urban Explosive",450000,[]],

						["SmokeShell",nil,1000,[]],
						["SmokeShellRed",nil,1000,[]],
						["SmokeShellGreen",nil,1000,[]],
						["SmokeShellPurple",nil,1000,[]],
						["HandGrenade",nil,50000,[]],

						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemCompass","Compass",50,[]],
						["ItemWatch","Watch",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Binocular","Binocular",1000,[]],
						["Rangefinder",nil,2000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]],
						["O_NVGoggles_grn_F",nil,1000,[]],
						["O_NVGoggles_hex_F",nil,1000,[]],
						["O_NVGoggles_ghex_F",nil,1000,[]],
						["O_NVGoggles_urb_F",nil,1000,[]]
					]
				];
			};
		};
	};

	case "gun": {
		switch(true) do {
			case (playerSide != civilian): {"You are not a civilian!"};
			case (eden_newsTeam): {"You cannot access this store while acting as a News Member!"};
			case (!license_civ_gun): {"You don't have a Firearms License!"};
			default {
				["Firearms Shop",
					[
						["hgun_Rook40_F","Rook-40 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_P07_khk_F","P07 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_01_F",nil,3500,[["10Rnd_9x21_Mag",nil,400]]],
						["hgun_ACPC2_F","ACP-C2 .45",40000,[["9Rnd_45ACP_Mag",".45 9Rnd Mag",500]]],
						["hgun_Pistol_Signal_F","Starter pistol",5000,[["6Rnd_GreenSignal_F","6Rnd Green Signal",500],["6Rnd_RedSignal_F","6Rnd Red Signal",500]]],

						["hgun_PDW2000_F","PDW2000 9 mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500]]],
						["SMG_05_F",nil,11000,[["30Rnd_9x21_Mag_SMG_02",nil,500],["30Rnd_9x21_Mag_SMG_02_Tracer_Green",nil,500]]],

						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]]
					]
				];
			};
		};
	};

	case "wplgun": {
		switch(true) do {
			case (playerSide != civilian): {"You are not a civilian!"};
			case (eden_newsTeam): {"You cannot access this store while acting as a News Member!"};
			case (!license_civ_gun): {"You don't have a Firearms License!"};
			case (!license_civ_wpl): {"You don't have an Workers Protection License!"};
			default {
				["Workers Protection Gun Shop",
					[

						["hgun_Rook40_F","Rook-40 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_P07_khk_F","P07 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_Pistol_01_F",nil,3500,[["10Rnd_9x21_Mag",nil,400]]],
						["hgun_ACPC2_F","ACP-C2 .45",7000,[["9Rnd_45ACP_Mag",".45 9Rnd Mag",500]]],
						["hgun_Pistol_heavy_01_F","4-five .45",7500,[["11Rnd_45ACP_Mag",".45 11Rnd Mag",500]]],

						["hgun_PDW2000_F","PDW2000 9 mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500]]],
						["SMG_05_F",nil,11000,[["30Rnd_9x21_Mag_SMG_02",nil,500],["30Rnd_9x21_Mag_SMG_02_Tracer_Green",nil,500]]],
						["SMG_01_F",nil,40000,[["30Rnd_45ACP_Mag_SMG_01",nil,500]]],
						["arifle_TRG20_F","TRG-20 5.56 mm",50000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_SPAR_01_khk_F",nil,55000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_Katiba_C_F",nil,65000,[["30Rnd_65x39_caseless_green",nil,600]]],

						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]],
						["optic_Holosight","MK17 Holosight",1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_ERCO_khk_F",nil,1000,[]],
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemCompass","Compass",50,[]],
						["Binocular","Binocular",1000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]]
					]
				];
			};
		};
	};

	case "gang": {
		switch(true) do {
			case (playerSide != civilian): {"You are not a civilian!"};
			case (eden_newsTeam): {"You cannot access this store while acting as a News Member!"};
			default {
				["Black Market Armament",
					[
						["hgun_Rook40_F","Rook-40 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["hgun_PDW2000_F","PDW2000 9 mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500]]],
						["hgun_P07_khk_F","P07 9mm",5000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["sgun_HunterShotgun_01_sawedoff_F",nil,45000,[["2Rnd_12Gauge_Pellets",nil,500]]],
						["SMG_05_F",nil,11000,[["30Rnd_9x21_Mag_SMG_02",nil,500],["30Rnd_9x21_Mag_SMG_02_Tracer_Green",nil,500]]],
						["hgun_Pistol_01_F",nil,3500,[["10Rnd_9x21_Mag",nil,400]]],
						["arifle_SDAR_F","SDAR 5.56 mm",25000,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",500]]],
						["SMG_03C_TR_camo","P90",60000,[["50Rnd_570x28_SMG_03",nil,10]]],
						["SMG_01_F","Vermin SMG .45 ACP",40000,[["30Rnd_45ACP_Mag_SMG_01",".45 ACP 30Rnd Vermin Mag",500]]],
						["arifle_Mk20C_F","Mk20C 5.56 mm (C)",50000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_TRG21_F","TRG-21 5.56mm",50000,[["30Rnd_556x45_Stanag","5.56mm 30rnd STANAG Mag",500]]],
						["arifle_AKS_F",nil,50000,[["30Rnd_545x39_Mag_F",nil,500],["30Rnd_545x39_Mag_Tracer_Green_F",nil,500]]],
						["arifle_CTAR_blk_F",nil,55000,[["30Rnd_580x42_Mag_F",nil,500],["30Rnd_580x42_Mag_Tracer_F",nil,500]]],
						["arifle_CTARS_blk_F",nil,70000,[["100Rnd_580x42_Mag_F",nil,3000],["100Rnd_580x42_Mag_Tracer_F",nil,3000]]],

						["acc_flashlight","Flashlight",500,[]],
						["acc_flashlight_pistol","Pistol Flashlight",500,[]],
						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_Holosight","MK17 Holosight",1000,[]],
						["optic_Holosight_blk_F", "Mk17 Holosight Black",500,[]],
						["optic_Holosight_smg_blk_F", "Mk17 SMG Holosight Black",500,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_Arco","ARCO Scope",1000,[]],
						["optic_Arco_blk_F","ARCO Scope",1000,[]],
						["optic_ERCO_blk_F",nil,1000,[]],
						["optic_ERCO_khk_F",nil,1000,[]],
						["acc_pointer_IR","IR Laser Pointer",500,[]],

						["SmokeShell",nil,1000,[]],
						["SmokeShellRed",nil,1000,[]],
						["SmokeShellGreen",nil,1000,[]],
						["SmokeShellPurple",nil,1000,[]],
						["HandGrenade",nil,50000,[]],

						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemCompass","Compass",50,[]],
						["MineDetector","Mine Detector",10000,[]],
						["Binocular","Binocular",1000,[]],
						["Rangefinder",nil,2000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]]
					]
				];
			};
		};
	};

	case "vigilante": {
		switch(true) do {
			case (playerSide != civilian): {"You are not a civilian!"};
			case (eden_newsTeam): {"You cannot access this store while acting as a News Member!"};
			case (!license_civ_vigilante): {"You don't have a Vigilante license!"};
			case (!license_civ_gun): {"You don't have a Firearms license!"};
			case (eden_vigiarrests < 25): {
				["Vigilante Shop",
					[
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["acc_flashlight","Flashlight",500,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellRed",nil,1000,[]],
						["SmokeShellGreen",nil,1000,[]],
						["SmokeShellPurple",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["Binocular","Binocular",1000,[]],
						["Rangefinder",nil,2000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]]
					]
				];
			};
			case ((eden_vigiarrests >= 25) && (eden_vigiarrests < 50)): {
				["Vigilante Shop",
					[
						["SMG_02_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_ACPC2_F","ACP-C2 .45",15000,[["9Rnd_45ACP_Mag",".45 9Rnd Mag",500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["acc_flashlight","Flashlight",500,[]],
						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]],
						["optic_Holosight_smg","Holosight (100m)",1350,[]],
						["optic_Holosight","Holosight (200m)",1500,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellRed",nil,1000,[]],
						["SmokeShellGreen",nil,1000,[]],
						["SmokeShellPurple",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["Binocular","Binocular",1000,[]],
						["Rangefinder",nil,2000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]]
					]
				];
			};
			case ((eden_vigiarrests >= 50) && (eden_vigiarrests < 100)): {
				["Vigilante Shop",
					[
						["SMG_02_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_ACPC2_F","ACP-C2 .45",15000,[["9Rnd_45ACP_Mag",".45 9Rnd Mag",500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["acc_flashlight","Flashlight",500,[]],
						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]],
						["optic_Holosight_smg","Holosight (100m)",1350,[]],
						["optic_Holosight","Holosight (200m)",1500,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellRed",nil,1000,[]],
						["SmokeShellGreen",nil,1000,[]],
						["SmokeShellPurple",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["Binocular","Binocular",1000,[]],
						["Rangefinder",nil,2000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]]
					]
				];
			};
			case ((eden_vigiarrests >= 100) && (eden_vigiarrests < 200)): {
				["Vigilante Shop",
					[
						["arifle_SPAR_01_snd_F",nil,100000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_SPAR_01_GL_snd_F",nil,125000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["SMG_02_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_ACPC2_F","ACP-C2 .45",15000,[["9Rnd_45ACP_Mag",".45 9Rnd Mag",500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["acc_flashlight","Flashlight",500,[]],
						["optic_Arco","Arco Scope (Sand)",1000,[]],
						["optic_Arco_blk_F","Arco Scope",1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]],
						["optic_Holosight_smg","Holosight (100m)",1350,[]],
						["optic_Holosight","Holosight (200m)",1500,[]],
						["bipod_01_F_snd","bipod (sand)",10000,[]],
						["1Rnd_Smoke_Grenade_shell","Smoke Grenade",10000,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellRed",nil,1000,[]],
						["SmokeShellGreen",nil,1000,[]],
						["SmokeShellPurple",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["Binocular","Binocular",1000,[]],
						["Rangefinder",nil,2000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]]
					]
				];
			};
			case (eden_vigiarrests >= 200): {
				["Vigilante Shop",
					[
						["arifle_SPAR_02_snd_F",nil,125000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_SPAR_01_snd_F",nil,100000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["arifle_SPAR_01_GL_snd_F",nil,125000,[["30Rnd_556x45_Stanag",nil,500],["30Rnd_556x45_Stanag_Tracer_Red",nil,500]]],
						["SMG_02_F","Sting 9mm",25000,[["30Rnd_9x21_Mag_SMG_02","9mm 30Rnd Mag",500]]],
						["hgun_ACPC2_F","ACP-C2 .45",15000,[["9Rnd_45ACP_Mag",".45 9Rnd Mag",500]]],
						["hgun_P07_F","P07 9mm",10000,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",500],["16Rnd_9x21_Mag",nil,500]]],
						["acc_flashlight","Flashlight",500,[]],
						["optic_Arco","Arco Scope (Sand)",1000,[]],
						["optic_Arco_blk_F","Arco Scope",1000,[]],
						["optic_Hamr","RCO Scope",1000,[]],
						["optic_MRCO","MRCO Scope",1000,[]],
						["optic_ACO_grn","ACO (Green)",1000,[]],
						["optic_Aco","ACO (Red)",1000,[]],
						["optic_Holosight_smg","Holosight (100m)",1350,[]],
						["optic_Holosight","Holosight (200m)",1500,[]],
						["bipod_01_F_snd","Bipod (Sand)",10000,[]],
						["1Rnd_Smoke_Grenade_shell","Smoke Grenade",10000,[]],
						["SmokeShell",nil,1000,[]],
						["SmokeShellRed",nil,1000,[]],
						["SmokeShellGreen",nil,1000,[]],
						["SmokeShellPurple",nil,1000,[]],
						["SmokeShellBlue",nil,1000,[]],
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemWatch","Watch",50,[]],
						["ItemCompass","Compass",50,[]],
						["Binocular","Binocular",1000,[]],
						["Rangefinder",nil,2000,[]],
						["NVGoggles_INDEP","Green Night Vision Goggles",150,[]],
						["NVGoggles","Brown Night Vision Goggles",100,[]]
					]
				];
			};
			default {};
		};
	};

	case "news": {
		switch (true) do {
			case (__GETC__(life_newslevel) < 1): {"You are not a News Team Member!"};
			case (!eden_newsTeam): {"You cannot access this store while acting as a News Member!"};
			case (playerSide != independent): {"You are not a civilian!"};
			case (__GETC__(life_newslevel) <= 2): {
				["News Team Equipment",
					[
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemCompass","Compass",50,[]],
						["Binocular","Binocular",1000,[]],
						["NVGoggles_INDEP",nil,1200,[]],
						["Chemlight_red","Red Chemlight",5000,[]],
						["Chemlight_green","Green Chemlight",5000,[]],
						["Chemlight_blue","Blue Chemlight",5000,[]],
						["Rangefinder",nil,300,[]]
					]
				];
			};
			case (__GETC__(life_newslevel) >= 3): {
				["News Team Equipment",
					[
						["I_UAV_01_backpack_F","News Drone",15000,[]],
						["I_UavTerminal","Drone Terminal",1000,[]],
						["FirstAidKit","First Aid Kit",500,[]],
						["ToolKit","Tool Kit",500,[]],
						["ItemGPS","GPS",50,[]],
						["ItemMap","Map",50,[]],
						["ItemCompass","Compass",50,[]],
						["Binocular","Binocular",1000,[]],
						["NVGoggles_INDEP",nil,1200,[]],
						["Chemlight_red","Red Chemlight",5000,[]],
						["Chemlight_green","Green Chemlight",5000,[]],
						["Chemlight_blue","Blue Chemlight",5000,[]],
						["Rangefinder",nil,300,[]]
					]
				];
			};
		};
	};

	case "bwadmin": {
		switch (true) do  {
			case (__GETC__(life_adminlevel) <= 2): {"You are not of sufficient admin level!"};
			case (__GETC__(life_adminlevel) > 2): {
				["Admin BW Weapons",
					[
						["srifle_DMR_06_camo_F",nil,0,[["20Rnd_762x51_Mag",nil,0]]],
						["srifle_EBR_F",nil,0,[["20Rnd_762x51_Mag",nil,0]]],
						["srifle_DMR_03_tan_F",nil,0,[["20Rnd_762x51_Mag",nil,0]]],
						["arifle_MX_F",nil,0,[["30Rnd_65x39_caseless_mag",nil,0]]],
						["arifle_MXM_F",nil,0,[["30Rnd_65x39_caseless_mag",nil,0]]],
						["srifle_DMR_07_ghex_F",nil,0,[["20Rnd_650x39_Cased_Mag_F",nil,0]]],
						["arifle_SPAR_03_khk_F",nil,0,[["20Rnd_762x51_Mag",nil,0]]],
						["arifle_Katiba_F",nil,0,[["30Rnd_65x39_caseless_green",nil,0]]],
						["srifle_DMR_02_camo_F",nil,0,[["10Rnd_338_Mag",nil,0]]],
						["launch_RPG7_F",nil,0,[["RPG7_F",nil,0]]],
						["launch_Titan_F",nil,0,[["Titan_AA",nil,0]]],
						["arifle_AK12_F",nil,0,[["30Rnd_762x39_Mag_F",nil,0]]],
						["LMG_03_F",nil,0,[["200Rnd_556x45_Box_F",nil,0]]],
						["LMG_Mk200_F",nil,0,[["200Rnd_65x39_cased_Box",nil,0]]],
						["arifle_ARX_ghex_F",nil,0,[["10Rnd_50BW_Mag_F",nil,0]]],
						["srifle_DMR_04_Tan_F",nil,0,[["10Rnd_127x54_Mag",nil,0]]],
						["150Rnd_762x54_Box_Tracer",nil,5,[]],
						["acc_pointer_IR",nil,0,[]],
						["optic_Arco",nil,0,[]],
						["Rangefinder",nil,0,[]],
						["bipod_01_F_mtp",nil,0,[]],
						["bipod_01_F_blk",nil,0,[]],
						["bipod_01_F_snd",nil,0,[]],
						["optic_Hamr",nil,0,[]],
						["optic_Hamr_khk_F",nil,0,[]],
						["optic_MRCO",nil,0,[]],
						["optic_Arco_blk_F",nil,0,[]],
						["optic_DMS",nil,0,[]],
						["muzzle_snds_B","7.62mm Suppressor",0,[]],
						["muzzle_snds_L","9mm Suppressor",0,[]],
						["muzzle_snds_H","6.5mm Suppressor",0,[]],
						["muzzle_snds_M","5.56 Suppressor",50,[]],
						["muzzle_snds_58_blk_F","5.8 Stealth Sound Suppressor",50,[]],
						["muzzle_snds_65_TI_blk_F","6.5 Stealth Sound Suppressor",50,[]],
						["SLAMDirectionalMine_Wire_Mag",nil,0,[]],
						["DemoCharge_Remote_Mag",nil,0,[]],
						["HandGrenade",nil,0,[]],
						["MiniGrenade",nil,0,[]],
						["APERSTripMine_Wire_Mag",nil,0,[]],
						["ClaymoreDirectionalMine_Remote_Mag",nil,0,[]],
						["HandGrenade_Stone",nil,0,[]]
					]
				];
			};
		};
	};

	case "copadmin": {
		switch (true) do  {
			case (__GETC__(life_adminlevel) <= 2): {"You are not of sufficient admin level!"};
			case (__GETC__(life_adminlevel) > 2): {
				["Admin Cop Weapons",
					[
						["hgun_P07_F","P07 9mm",0,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",0],["16Rnd_9x21_Mag",nil,0]]],
						["hgun_Pistol_heavy_02_F","Zubr .45",0,[["6Rnd_45ACP_Cylinder",".45 ACP 6Rnd Cylinder",0]]],
						["hgun_Pistol_heavy_01_F","4-five .45",0,[["11Rnd_45ACP_Mag",".45 11Rnd Mag",0]]],
						["SMG_02_ACO_F","Sting 9mm",0,[["30Rnd_9x21_Mag","9mm 30Rnd Mag",0],["30Rnd_9x21_Red_Mag","9mm 30Rnd Tracer Mag",0]]],
						["arifle_SDAR_F","SDAR 5.56 mm",0,[["20Rnd_556x45_UW_mag","5.56mm 20Rnd Dual Purpose Mag",0]]],
						["arifle_SPAR_01_blk_F",nil,0,[["30Rnd_556x45_Stanag",nil,0],["30Rnd_556x45_Stanag_Tracer_Red",nil,0]]],
						["arifle_MX_Black_F","MX 6.5 mm",0,[["30Rnd_65x39_caseless_mag","6.5mm 30Rnd STANAG Mag",0],["30Rnd_65x39_caseless_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",0]]],
						["arifle_MX_GL_Black_F","MX GL 6.5 mm",0,[["30Rnd_65x39_caseless_mag","6.5mm 30Rnd STANAG Mag",0],["30Rnd_65x39_caseless_mag_Tracer","6.5mm 30Rnd STANAG Tracer Mag",0]]],
						["arifle_MXM_Black_F","MXM 6.5mm",0,[["30Rnd_65x39_caseless_mag","6.5mm 30Rnd STANAG Mag",0]]],
						["arifle_MX_SW_Black_F","MX-SW 6.5mm",0,[["100Rnd_65x39_caseless_mag","6.5mm 100Rnd STANAG Mag",0],["100Rnd_65x39_caseless_mag_Tracer","6.5mm 100Rnd Tracer Mag",0]]],
						["srifle_DMR_03_F","Mk1 7.62mm",0,[["20Rnd_762x51_Mag","7.62mm 20Rnd Mag",0]]],
						["srifle_DMR_02_F","MAR-10 .338",0,[["10Rnd_338_Mag",".338 LM 10 Rnd Mag",0]]],
						["arifle_SPAR_01_GL_blk_F",nil,0,[["30Rnd_556x45_Stanag",nil,0],["30Rnd_556x45_Stanag_Tracer_Red",nil,0]]],
						["arifle_SPAR_02_blk_F",nil,0,[["150Rnd_556x45_Drum_Mag_F",nil,0],["150Rnd_556x45_Drum_Mag_Tracer_F",nil,0]]],
						["arifle_SPAR_03_blk_F",nil,0,[["20Rnd_762x51_Mag",nil,0]]],
						["arifle_ARX_blk_F",nil,0,[["30Rnd_65x39_caseless_green",nil,0]]],
						["srifle_DMR_07_blk_F",nil,0,[["20Rnd_650x39_Cased_Mag_F",nil,0]]],
						["muzzle_snds_338_black",nil,0,[]]
					]
				];
			};
		};
	};

	case "adminclothing": {
		switch (true) do  {
			case (__GETC__(life_adminlevel) <= 2): {"You are not of sufficient admin level!"};
			case (__GETC__(life_adminlevel) > 2): {
				["Restricted Clothing",
					[
						["U_O_R_Gorka_01_F",nil,0],
						["U_O_R_Gorka_01_brown_F",nil,0],
						["U_O_R_Gorka_01_camo_F",nil,0],
						["U_O_R_Gorka_01_black_F",nil,0],
						["U_O_CombatUniform_ocamo",nil,0,[]],
						["U_O_CombatUniform_oucamo",nil,0,[]],
						["U_O_SpecopsUniform_ocamo",nil,0,[]],
						["U_O_PilotCoveralls",nil,0,[]],
						["U_O_T_FullGhillie_tna_F",nil,0,[]],
						["U_B_Protagonist_VR",nil,0,[]],
						["U_I_Protagonist_VR",nil,0,[]],
						["U_O_Protagonist_VR",nil,0,[]],
						["U_O_CombatUniform_ocamo",nil,0,[]],
						["U_O_CombatUniform_ocamo",nil,0,[]],
						["U_O_CombatUniform_ocamo",nil,0,[]],
						["U_O_CombatUniform_ocamo",nil,0,[]],
						["U_O_GhillieSuit",nil,0,[]],
						["U_O_CombatUniform_oucamo",nil,0,[]],
						["H_PilotHelmetFighter_O",nil,0,[]],
						["H_HelmetLeaderO_ghex_F",nil,0,[]],
						["NVGogglesB_gry_F",nil,0,[]],
						["H_CrewHelmetHeli_I",nil,0,[]],
						["H_CrewHelmetHeli_O",nil,0,[]],
						["G_Shades_Black",nil,0,[]],
						["V_PlateCarrierSpec_rgr",nil,0,[]],
						["V_PlateCarrierIAGL_oli",nil,0,[]],
						["V_HarnessOGL_brn","Suicide Vest",0,[]],
						["V_PlateCarrierIA1_dgtl",nil,0,[]],
						["U_Rangemaster","Basic Cop Uniform",0,[]],
						["U_Marshal","AHP Uniform",0,[]],
						["U_I_CombatUniform","Senior Uniform",0,[]],
						["U_B_CombatUniform_mcam","Upgraded Senior Uniform",0,[]],
						["H_Cap_police","Police Cap",0,[]],
						["H_MilCap_blue","Cop Blue Hat",0,[]],
						["H_MilCap_gry","Cop Grey Hat",0,[]],
						["H_Beret_blk_POLICE","Beret",0,[]],
						["H_Cap_Black_IDAP_F",nil,0,[]],
						["H_Beret_Colonel","Beret [Colonel]",0,[]],
						["H_Beret_02","Beret [NATO]",0,[]],
						["H_PilotHelmetHeli_B","Sergeant Hat",0,[]],
						["H_HelmetSpecB_blk","Stahlhelm",0,[]],
						["H_PASGT_basic_black_F",nil,0,[]],
						["H_PilotHelmetFighter_B","Pilot Helmet [NATO]",0,[]],
						["H_CrewHelmetHeli_B","Alternative Captain Hat",0,[]],
						["H_HelmetB_black",nil,0,[]],
						["H_HelmetB_TI_tna_F",nil,0,[]],
						["V_TacVest_blk","Cardboard Vest",0,[]],
						["V_TacVest_blk_POLICE","APD Tactical Vest",0,[]],
						["V_PlateCarrier1_blk","APD Carrier Lite",0,[]],
						["V_PlateCarrier2_blk","Improved Senior Vest",0,[]],
						["V_PlateCarrierSpec_blk","Improved Tactical Vest V2",0,[]],
						["B_Carryall_oucamo","Invis. Carryall",0,[]],
						["B_Kitbag_rgr","Invis. Kitbag",0,[]]
					]
				];
			};
		};
	};

	case "war_market": {
		switch(true) do {
			case (playerSide != civilian): {"You are not a civilian!"};
			case (eden_newsTeam): {"You cannot access this store while acting as a News Member!"};
			case (__GETC__(life_adminlevel) isEqualTo 4): {
				["Senior Staff Warpoint Market",
					[
						["arifle_MX_F",nil,1,[["30Rnd_65x39_caseless_mag",nil,1]]],
						["arifle_MXM_F",nil,1,[["30Rnd_65x39_caseless_mag",nil,1]]],
						["arifle_MX_SW_F",nil,1,[["100Rnd_65x39_caseless_mag",nil,1]]],
						["srifle_DMR_07_ghex_F",nil,1,[["20Rnd_650x39_Cased_Mag_F",nil,1]]],
						["arifle_SPAR_03_khk_F",nil,1,[["20Rnd_762x51_Mag",nil,1]]],
						["srifle_EBR_F",nil,1,[["20Rnd_762x51_Mag",nil,1]]],
						["srifle_DMR_03_tan_F",nil,1,[["20Rnd_762x51_Mag",nil,1]]],
						["srifle_DMR_02_camo_F",nil,1,[["10Rnd_338_Mag",nil,1]]],
						["srifle_DMR_04_Tan_F",nil,1,[["10Rnd_127x54_Mag",nil,1]]],
						["SMG_03_TR_camo",nil,1,[["50Rnd_570x28_SMG_03",nil,1]]],
						["arifle_ARX_ghex_F",nil,1,[["30Rnd_65x39_caseless_green",nil,1],["30Rnd_65x39_caseless_green_mag_Tracer",nil,1]]],
						["LMG_Mk200_F",nil,1,[["200Rnd_65x39_cased_Box",nil,1]]],
						["LMG_Zafir_F",nil,1,[["150Rnd_762x54_Box_Tracer",nil,1]]],
						["launch_Titan_F",nil,1,[["Titan_AA",nil,1]]],
						["launch_B_Titan_olive_F",nil,1,[["Titan_AA",nil,1]]],
						["launch_I_Titan_F",nil,1,[["Titan_AA",nil,1]]],
						["launch_RPG7_F",nil,1,[["RPG7_F",nil,1]]],
						["muzzle_snds_L","9mm Suppressor",1,[]],
						["muzzle_snds_M","5.56 Suppressor",1,[]],
						["muzzle_snds_58_blk_F","5.8 Stealth Sound Suppressor",1,[]],
						["muzzle_snds_H","6.5 Suppressor",1,[]],
						["muzzle_snds_65_TI_blk_F","6.5 Stealth Sound Suppressor",1,[]],
						["muzzle_snds_338_black",".338 Suppressor",1,[]],
						["muzzle_snds_570","5.7mm Suppressor",30,[]],
						["muzzle_snds_acp",".45mm Suppressor",5,[]],
						["NVGogglesB_gry_F",nil,1,[]],
						["10Rnd_50BW_Mag_F",nil,1,[]],
						["6Rnd_12Gauge_Slug",nil,1,[]],
						["6Rnd_12Gauge_Pellets",nil,1,[]],
						["DemoCharge_Remote_Mag",nil,1,[]],
						["SLAMDirectionalMine_Wire_Mag",nil,1,[]],
						["HandGrenade",nil,1,[]]
					]
				];
			};
			default {
				["Warpoint Market",
					[

						["arifle_MX_F",nil,5,[["30Rnd_65x39_caseless_mag",nil,1]]],
						["arifle_MXM_F",nil,6,[["30Rnd_65x39_caseless_mag",nil,1]]],
						["arifle_MX_SW_F",nil,9,[["100Rnd_65x39_caseless_mag",nil,2]]],
						["SMG_03_TR_camo",nil,7,[["50Rnd_570x28_SMG_03",nil,1]]],
						["srifle_DMR_07_ghex_F",nil,6,[["20Rnd_650x39_Cased_Mag_F",nil,1]]],
						["arifle_SPAR_03_khk_F",nil,6,[["20Rnd_762x51_Mag",nil,1]]],
						["srifle_EBR_F",nil,7,[["20Rnd_762x51_Mag",nil,2]]],
						["srifle_DMR_03_tan_F",nil,8,[["20Rnd_762x51_Mag",nil,1]]],
						["srifle_DMR_04_Tan_F",nil,30,[["10Rnd_127x54_Mag",nil,1]]],
						["arifle_ARX_ghex_F",nil,6,[["30Rnd_65x39_caseless_green",nil,1],["30Rnd_65x39_caseless_green_mag_Tracer",nil,1]]],
						["LMG_Mk200_F",nil,10,[["200Rnd_65x39_cased_Box",nil,2]]],
						["LMG_Mk200_black_F",nil,10,[["200Rnd_65x39_cased_Box",nil,2]]],
						["arifle_AK12_arid_F",nil,7,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,1],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,1]]],
						["arifle_AK12_lush_F",nil,7,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,1],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,1]]],
						["arifle_AK12U_arid_F",nil,7,[["30Rnd_762x39_AK12_Arid_Mag_F",nil,1],["30Rnd_762x39_AK12_Arid_Mag_Tracer_F",nil,1]]],
						["arifle_AK12U_lush_F",nil,7,[["30Rnd_762x39_AK12_Lush_Mag_F",nil,1],["30Rnd_762x39_AK12_Lush_Mag_Tracer_F",nil,1]]],
						["arifle_RPK12_arid_F",nil,12,[["75Rnd_762x39_Mag_F",nil,5],["75Rnd_762x39_Mag_Tracer_F",nil,5]]],
						["arifle_RPK12_lush_F",nil,12,[["75Rnd_762x39_Mag_F",nil,5],["75Rnd_762x39_Mag_Tracer_F",nil,5]]],
						["arifle_MSBS65_F",nil,5,[["30Rnd_65x39_caseless_msbs_mag",nil,1],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,1]]],
						["arifle_MSBS65_camo_F",nil,5,[["30Rnd_65x39_caseless_msbs_mag",nil,1],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,1]]],
						["arifle_MSBS65_sand_F",nil,5,[["30Rnd_65x39_caseless_msbs_mag",nil,1],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,1]]],
						["arifle_MSBS65_Mark_F",nil,6,[["30Rnd_65x39_caseless_msbs_mag",nil,1],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,1]]],
						["arifle_MSBS65_Mark_camo_F",nil,6,[["30Rnd_65x39_caseless_msbs_mag",nil,1],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,1]]],
						["arifle_MSBS65_Mark_sand_F",nil,6,[["30Rnd_65x39_caseless_msbs_mag",nil,1],["30Rnd_65x39_caseless_msbs_mag_Tracer",nil,1]]],
						["sgun_HunterShotgun_01_sawedoff_F",nil,4,[["2Rnd_12Gauge_Pellets",nil,1],["2Rnd_12Gauge_Slug",nil,3]]],
						["arifle_RPK12_arid_F",nil,12,[["75Rnd_762x39_Mag_F",nil,5],["75Rnd_762x39_Mag_Tracer_F",nil,5]]],
						["launch_Titan_F",nil,30,[["Titan_AA",nil,10]]],
						["launch_B_Titan_olive_F",nil,30,[["Titan_AA",nil,10]]],
						["launch_I_Titan_F",nil,30,[["Titan_AA",nil,10]]],
						["launch_RPG7_F",nil,75,[["RPG7_F",nil,10]]],
						["muzzle_snds_L","9mm Suppressor",5,[]],
						["muzzle_snds_M","5.56mm Suppressor",30,[]],
						["muzzle_snds_58_blk_F","5.8mm Suppressor",35,[]],
						["muzzle_snds_H","6.5mm Suppressor",50,[]],
						["muzzle_snds_65_TI_blk_F","6.5mm Suppressor",50,[]],
						["muzzle_snds_570","5.7mm Suppressor",30,[]],
						["muzzle_snds_acp",".45mm Suppressor",5,[]],
						["150Rnd_556x45_Drum_Mag_Tracer_F",nil,1,[]],
						["150Rnd_762x54_Box_Tracer",nil,5,[]],
						["10Rnd_338_Mag",nil,5,[]],
						["10Rnd_50BW_Mag_F",nil,20,[]],
						["6Rnd_12Gauge_Slug",nil,4,[]],
						["6Rnd_12Gauge_Pellets",nil,2,[]],
						["DemoCharge_Remote_Mag",nil,12,[]],
						["SLAMDirectionalMine_Wire_Mag",nil,10,[]],
						["HandGrenade",nil,4,[]]
					]
				];
			};
		};
	};

	case "genstore": {
		["Altis General Store",
			[
				["ItemGPS","GPS",50,[]],
				["ItemMap","Map",50,[]],
				["ItemCompass","Compass",50,[]],
				["ToolKit","Tool Kit",500,[]],
				["FirstAidKit","FirstAid Kit",500,[]],
				["NVGoggles","Night Vision Goggles",100,[]],
				["Binocular",nil,1000,[]],
				["Rangefinder",nil,2000,[]],
				["Chemlight_red","Red Chemlight",5000,[]],
				["Chemlight_green","Green Chemlight",5000,[]],
				["Chemlight_blue","Blue Chemlight",5000,[]]
			]
		];
	};

	case "dopecratesupplies": {
		["Emergency Supplies",
			[
				["ToolKit","Tool Kit",500,[]],
				["FirstAidKit","FirstAid Kit",500,[]],
				["NVGoggles","Night Vision Goggles",100,[]]
			]
		];
	};
};
