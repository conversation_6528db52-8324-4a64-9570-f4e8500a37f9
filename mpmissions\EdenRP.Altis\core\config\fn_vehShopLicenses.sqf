//  File: fn_vehShopLicenses.sqf
//	Author: <PERSON> "<PERSON>" Boardwine

//	Description: Checks what shop it is and sometimes the vehicle to determine whether or not they have the license.

//	Returns:
//	TRUE if they have the license or are allowed to get that specific vehicle without having that license.
//	FALSE if they don't have the license or are not allowed to obtain that vehicle.
private["_veh","_ret","_here"];
_veh = _this select 0;
_ret = false;


if(_veh == "B_Quadbike_01_F") exitWith {true}; //ATV's don't need to require a license anymore.

switch (life_veh_shop select 0) do {
	case "med_shop": {_ret = true;};
	case "med_air_hs": {_ret = license_med_air;};
	case "med_ship": {_ret = license_med_cg;};
	case "civ_car": {_ret = license_civ_driver;};
	case "indp_news": {_ret = true;};
	case "civ_ship": {_ret = license_civ_boat;};
	case "civ_air": {_ret = license_civ_air;};
	case "civ_plane": {_ret = license_civ_air;};
	case "civ_kart": {_ret = license_civ_driver;};
	case "cop_plane": {_ret = license_cop_air;};
	case "cop_air": {_ret = license_cop_air;};
	case "cop_airhq": {_ret = license_cop_air;};
	case "civ_truck":	{_ret = license_civ_truck;};
	case "reb_v": {_ret = license_civ_rebel;};
	case "war_v": {_ret = license_civ_rebel;};
	case "reb_boat": {_ret = license_civ_rebel;};
	case "cop_car": {_ret = true;};
	case "vigi_car": {_ret = license_civ_vigilante;};
	case "cop_ship": {
		if(_veh == "B_Boat_Armed_01_minigun_F") then {
			_ret = license_cop_cg;
		} else {
			_ret = true;
		};
	};
};

_ret;