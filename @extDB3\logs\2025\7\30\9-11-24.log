
extDB3: https://bitbucket.org/torndeco/extdb3/wiki/Home
extDB3: Version: 1.033
extDB3: Windows Version
Message: All development for extDB3 is done on a Linux Dedicated Server
Message: If you would like to Donate to extDB3 Development
Message: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=2SUEFTGABTAM2
Message: Also leave a message if there is any particular feature you would like to see added.
Message: Thanks for all the people that have donated.
Message: Torndeco: 18/05/15

Message: This version was modified and include some improvement
Message: The lib was recompiled using new version of TBBMalloc, Boost and MariaDB connector
Message: The worker threads limitation was removed
Message: Steez: 30/10/2020


extDB3: Found extdb3-conf.ini
extDB3: Detected 16 Cores, Setting up 6 Worker Threads
extDB3: Detected 16 Cores, Setting up 16 Worker Threads
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...


[09:11:26:006578 -04:00] [Thread 28364] extDB3: SQL: Initialized: Add Quotes around TEXT Datatypes mode: 2
[09:11:26:006612 -04:00] [Thread 28364] extDB3: SQL: Initialized: NULL = ""
[09:11:26:006642 -04:00] [Thread 28364] extDB3: Locked
