
extDB3: https://bitbucket.org/torndeco/extdb3/wiki/Home
extDB3: Version: 1.033
extDB3: Windows Version
Message: All development for extDB3 is done on a Linux Dedicated Server
Message: If you would like to Donate to extDB3 Development
Message: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=2SUEFTGABTAM2
Message: Also leave a message if there is any particular feature you would like to see added.
Message: Thanks for all the people that have donated.
Message: Torndeco: 18/05/15

Message: This version was modified and include some improvement
Message: The lib was recompiled using new version of TBBMalloc, Boost and MariaDB connector
Message: The worker threads limitation was removed
Message: Steez: 30/10/2020


extDB3: Found extdb3-conf.ini
extDB3: Detected 16 Cores, Setting up 6 Worker Threads
extDB3: Detected 16 Cores, Setting up 16 Worker Threads
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...


[18:50:30:113223 -04:00] [Thread 25112] extDB3: SQL: Initialized: Add Quotes around TEXT Datatypes mode: 2
[18:50:30:113253 -04:00] [Thread 25112] extDB3: SQL: Initialized: NULL = ""
[18:50:30:113280 -04:00] [Thread 25112] extDB3: Locked
[18:50:30:153571 -04:00] [Thread 33264] extDB3: SQL: Error MariaDBQueryException: Unknown column 'houses.expires_on' in 'field list'
[18:50:30:153623 -04:00] [Thread 33264] extDB3: SQL: Error MariaDBQueryException: Input: SELECT houses.id, houses.pid, houses.pos, players.name, houses.player_keys, houses.inventory, houses.storageCapacity, houses.inAH, houses.oil, houses.physical_inventory, houses.physicalStorageCapacity, DATEDIFF(houses.expires_on, TIMESTAMP(CURRENT_DATE())) FROM houses INNER JOIN players ON houses.pid=players.playerid WHERE houses.owned='1' AND server='1' LIMIT 0,10
[18:50:30:153770 -04:00] [Thread 33264] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.selectMax does not exist
[18:50:30:153786 -04:00] [Thread 33264] extDB3: SQL: Error MariaDBQueryException: Input: CALL selectMax
[18:50:30:158031 -04:00] [Thread 33264] extDB3: SQL: Error MariaDBQueryException: 
[18:50:30:158070 -04:00] [Thread 33264] extDB3: SQL: Error MariaDBQueryException: Input: CALL deleteOldHouses1
[18:50:30:158133 -04:00] [Thread 26808] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.gangBuildingCleanup does not exist
[18:50:30:158178 -04:00] [Thread 26808] extDB3: SQL: Error MariaDBQueryException: Input: CALL gangBuildingCleanup
[18:50:30:181721 -04:00] [Thread 26808] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.updateMemberNames does not exist
[18:50:30:181730 -04:00] [Thread 33064] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.houseCleanup1 does not exist
[18:50:30:181746 -04:00] [Thread 33264] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.deleteDeadVehicles does not exist
[18:50:30:181754 -04:00] [Thread 20364] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.deleteOldGangs does not exist
[18:50:30:181764 -04:00] [Thread 23232] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.resetLifeVehicles1 does not exist
[18:50:30:181768 -04:00] [Thread 26808] extDB3: SQL: Error MariaDBQueryException: Input: CALL updateMemberNames
[18:50:30:181774 -04:00] [Thread 33140] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.giveCash does not exist
[18:50:30:181776 -04:00] [Thread 33612] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.deleteContracts does not exist
[18:50:30:181818 -04:00] [Thread 33064] extDB3: SQL: Error MariaDBQueryException: Input: CALL houseCleanup1
[18:50:30:181835 -04:00] [Thread 33264] extDB3: SQL: Error MariaDBQueryException: Input: CALL deleteDeadVehicles
[18:50:30:181849 -04:00] [Thread 20364] extDB3: SQL: Error MariaDBQueryException: Input: CALL deleteOldGangs
[18:50:30:181872 -04:00] [Thread 23232] extDB3: SQL: Error MariaDBQueryException: Input: CALL resetLifeVehicles1
[18:50:30:181912 -04:00] [Thread 33140] extDB3: SQL: Error MariaDBQueryException: Input: CALL giveCash
[18:50:30:181933 -04:00] [Thread 33612] extDB3: SQL: Error MariaDBQueryException: Input: CALL deleteContracts
