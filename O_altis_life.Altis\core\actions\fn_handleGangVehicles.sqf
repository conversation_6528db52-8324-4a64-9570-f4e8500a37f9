//	File: fn_handleGangVehicles.sqf
//	Author: ikiled
//	Description: <PERSON><PERSON> opening of player gang garages via NPCs

if(oev_garageCooldown > time) exitWith {hint "Please do not spam your garage. It may take a bit to show your vehicles if the server is under heavy load.";};
if !((count oev_gang_data) > 0) exitWith {hint "You must be in a gang to access the gang garage."};
if ((oev_gang_data select 2) < 2) exitWith {hint "You must be gang rank 2 or higher to access your gang garage."};

params ["_type","_spawn"];

[[getPlayerUID player,playerSide,_type,player,true,(oev_gang_data select 0)],"OES_fnc_getVehicles",false,false] spawn OEC_fnc_MP;
["Life_impound_menu"] call OEC_fnc_createDialog;
disableSerialization;
ctrlSetText[2802,"Fetching Vehicles...."];
oev_garage_sp = _spawn;
oev_garage_type = _type;
oev_garageCooldown = time+5;