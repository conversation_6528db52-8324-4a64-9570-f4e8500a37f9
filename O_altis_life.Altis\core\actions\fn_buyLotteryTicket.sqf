// File: fn_buyLotteryTicket.sqf
// Author: Fusah
// Description: Puts the user into the lottery!

params["_type"];
private _exit = false;
switch (_type) do {
	case "start": {
		if (life_lotteryCooldown) exitWith {hint "The lottery system is on cooldown, you are not able to buy lottery tickets at this time!"};
		if !(isNull objectParent player) exitWith {hint "You are not able to buy a lottery ticket while in a vehicle!"};
		if (oev_action_inUse) exitWith {hint "You are already performing another action!"};
		if (player getVariable ["restrained",false]) exitWith {};
		if (oev_isDowned) exitWith {};
		if (oev_cash < oev_lotteryPrice && oev_atmcash < oev_lotteryPrice) exitWith {hint "You don't have enough money to buy a lottery ticket!"};

		if !(life_runningLottery) then {
			if (isNil "serverStartTime" || isNil "serverCycleLength") exitWith {_exit = true};
			private _restartTime = round((serverCycleLength - (serverTime - serverStartTime)) / 60);
			if (_restartTime < 35) exitWith {_exit = true};
		};
		if (_exit) exitWith {hint "You are not able to buy lottery tickets at this time!"};

		oev_action_inUse = true;
		["check",player] remoteExec ["OES_fnc_handleLottery",2,false];
		titleText ["Processing...","PLAIN DOWN",.3];
		uiSleep 3;
		if (oev_inLottery) exitWith {hint "You have already bought tickets in the current lottery!"; oev_action_inUse = false};
		oev_action_inUse = false;
		hint "You are only able to buy up to 10 lottery tickets.";
		["life_buy_ticket"] call OEC_fnc_createDialog;
	};
	case "end": {
		private _val = ctrlText 3653;
		closeDialog 0;
		if !(isNil _val) exitWith {};
		if (!([_val] call OEC_fnc_isNumeric)) exitWith {hint "Please enter a valid number!"};
		_val = parseNumber _val;
		if ((_val) > 10) exitWith {hint "You may only buy up to 10 lottery tickets."};
		if ((_val) <= 0) exitWith {}; // Why bother
		if (oev_cash < (_val * oev_lotteryPrice) && oev_atmcash < (_val * oev_lotteryPrice)) exitWith {hint "You do not have enough money to buy that amount of lottery tickets!"};
		private _buy = [
			format ["Are you sure you would like to buy %2 ticket%3 for $%1? You must be online when the lottery ends to collect your reward if you win.",[oev_lotteryPrice*_val] call OEC_fnc_numberText, _val,if (_val > 1) then {"s"} else {""}],
			"Confirm",
			"Yes",
			"No"
		] call BIS_fnc_guiMessage;
		private _loss = _val * oev_lotteryPrice;
		if (_buy) then {
		if !(oev_cash < _loss) then {
			oev_cash = oev_cash - _loss;
			oev_cache_cash = oev_cache_cash - _loss;
			[0] call OEC_fnc_ClupdatePartial;
		} else {
			oev_atmcash = oev_atmcash - _loss;
			oev_cache_atmcash = oev_cache_atmcash - _loss;
			[1] call OEC_fnc_ClupdatePartial;
		};
		oev_inLottery = true;
		hint "Thank you for purchasing lottery tickets.. good luck!";
		["add",player,_val] remoteExec ["OES_fnc_handleLottery",2,false];
		[
			["event","Bought Lottery Tickets"],
			["player",name player],
			["player_id",getPlayerUID player],
			["tickets",_val],
			["value",[_loss] call OEC_fnc_numberText],
			["location",getPosATL player]
		] call OEC_fnc_logIt;
		};
	};
};
