#include <zmacro.h>
//	File: fn_handleGlow.sqf
//	Author: Fusah
//	Description: Handles underglow for vehicles.

if (true) exitWith {hint "This feature is temporarily disabled."};

if(scriptAvailable(5)) exitWith {hint "Please do not spam underglow!";};

params [
	["_veh",objNull]
];

private _donorLevel = (__GETC__(eden_donator));
private _pos = [];
private _color = [];

//if (_donorLevel < 15) exitWith {};
if (vehicle player != _veh) exitWith {};
if (vehicle player isEqualTo player) exitWith {};
if !(_veh isKindOf "LandVehicle") exitWith {};
if !((driver (vehicle player)) isEqualTo player) exitWith {hint "You must be the driver to active underglow!"};

private _dialog = findDisplay 33000;
private _list = _dialog displayCtrl 33006;
private _sel = lbCurSel _list;
private _arr = _list lbData _sel;
private _exe = false;

switch (true) do {
	case (typeOf _veh in ["C_Hatchback_01_F","C_Hatchback_01_sport_F"]): {_pos = [0,-.3,-1.01]; if (_donorLevel < 50) exitWith {_exe = true};};
	case (typeOf _veh in ["C_Offroad_01_F","C_Offroad_01_repair_F","I_G_Offroad_01_AT_F"]): {_pos = [0,-0.3,-1.1]; if (_donorLevel < 15) exitWith {_exe = true};};
	case (typeOf _veh isEqualTo "C_SUV_01_F"): {_pos = [0,-0.2,-1.1]; if (_donorLevel < 30) exitWith {_exe = true};};
	case (typeOf _veh isEqualTo "O_MRAP_02_F"): {_pos = [0,-1.5,-1.01]; if (_donorLevel < 100) exitWith {_exe = true};};
	case (typeOf _veh isEqualTo "B_Quadbike_01_F"): {_pos = [0,0,0]};
	case (typeof _veh isEqualTo "I_MRAP_03_F"): {_pos =[0,-.4,-1.01]; if (_donorLevel < 100) exitWith {_exe = true};};
	case (typeof _veh isEqualTo "B_MRAP_01_F"): {_pos = [0,-1.6,-1.2]; if (_donorLevel < 100) exitWith {_exe = true};};
};

if (_pos isEqualTo []) exitWith {hint "This vehicle does not currently support underglow!"};
if (_exe) exitWith {hint "You do not have the required donor level to add underglow to this vehicle!"};
//if (_color isEqualTo []) exitWith {hint "Please visit an upgrade station to add underglow to this vehicle!"};

if (_veh getVariable ['underActive',false]) exitWith {_veh setVariable ['underActive',false,true]; hint "You have removed underglow from this vehicle!";};
if (_sel isEqualTo -1) exitWith {hint "Please select a color!"};

hint "You have successfully put underglow on this vehicle!";

_color = parseSimpleArray _arr;
_veh setVariable ["underglow",_color,true];
_veh setVariable ["underActive",true,true];

uiSleep 1;

[[_pos,_color,_veh],"EDEN_fnc_handleGlowLights",-2,false] call EDEN_fnc_MP;