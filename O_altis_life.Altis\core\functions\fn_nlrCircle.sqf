//  File: fn_nlrCircle.sqf
//	Author: <PERSON> "Head Nigha" Kazi
//	Description: <PERSON><PERSON><PERSON> didnt like my copy pasta

private ["_dist","_count"];
while {true} do {
  waitUntil {sleep 15; ((count oev_deaths) > 0)};
  {
    if (time < (_x select 1)) then {
      _dist = player distance (_x select 0);
      _pos = (_x select 0);
      _count = (_x select 2);

      if (_dist < 1000) then {
        _count = _count + 1;
        hint parseText "<t color='#FF0000'><t size='2'><t align='center'>!!! ALERT !!!<br/><br/><t color='#FF5733'><t align='left'><t size='1'>You are currently breaking New Life Rule! Do not return within 1,000 meters of any location you were killed at until 15 minutes have past since time of death! Please leave the area immediately!";

        if (_count in [1,3,5,8,13,17]) then {
          [
            ["event","Broke NLR"],
            ["player",name player],
            ["player_id",getPlayerUID player],
            ["nlr_position",_pos],
            ["distance",_dist],
            ["position",getPosATL player]
          ] call OEC_fnc_logIt;
        };

        (oev_deaths select _forEachIndex) set [2,_count];
      };

      if(isNil{_x select 3}) then {
        _marker = createMarkerLocal [format ["nlr_marker_%1", floor(_x select 1)], [_pos select 0, _pos select 1]];
        _marker setMarkerSizeLocal [1000, 1000];
        _marker setMarkerColorLocal "ColorOrange";
        _marker setMarkerShapeLocal "ELLIPSE";
        _marker setMarkerAlphaLocal 0.7;
        _marker setMarkerTextLocal "NLR Zone";

        _marker_text = createMarkerLocal [format ["nlr_marker_text_%1", floor(_x select 1)], [_pos select 0, _pos select 1]];
        _marker_text setMarkerColorLocal "ColorBrown";
        _marker_text setMarkerTypeLocal "mil_objective";
        _marker_text setMarkerTextLocal format["You recently died here - NLR Active - %1m", [] call OEC_fnc_timeUntilRestart];

        (oev_deaths select _forEachIndex) set [3, true];
      };
    } else {
      if((_x select 3)) then {
        deleteMarkerLocal format["nlr_marker_%1", floor(_x select 1)];
        deleteMarkerLocal format["nlr_marker_text_%1", floor(_x select 1)];
      };
    };
  } forEach oev_deaths;
};
