
extDB3: https://bitbucket.org/torndeco/extdb3/wiki/Home
extDB3: Version: 1.033
extDB3: Windows Version
Message: All development for extDB3 is done on a Linux Dedicated Server
Message: If you would like to Donate to extDB3 Development
Message: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=2SUEFTGABTAM2
Message: Also leave a message if there is any particular feature you would like to see added.
Message: Thanks for all the people that have donated.
Message: Torndeco: 18/05/15

Message: This version was modified and include some improvement
Message: The lib was recompiled using new version of TBBMalloc, Boost and MariaDB connector
Message: The worker threads limitation was removed
Message: Steez: 30/10/2020


extDB3: Found extdb3-conf.ini
extDB3: Detected 16 Cores, Setting up 6 Worker Threads
extDB3: Detected 16 Cores, Setting up 16 Worker Threads
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...
extDB3: ...


[18:32:04:052342 -04:00] [Thread 33968] extDB3: SQL: Initialized: Add Quotes around TEXT Datatypes mode: 2
[18:32:04:052396 -04:00] [Thread 33968] extDB3: SQL: Initialized: NULL = ""
[18:32:04:052440 -04:00] [Thread 33968] extDB3: Locked
[18:32:04:128642 -04:00] [Thread 32972] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.gangBuildingCleanup does not exist
[18:32:04:128642 -04:00] [Thread 31288] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.selectMax does not exist
[18:32:04:128684 -04:00] [Thread 32972] extDB3: SQL: Error MariaDBQueryException: Input: CALL gangBuildingCleanup
[18:32:04:128703 -04:00] [Thread 31288] extDB3: SQL: Error MariaDBQueryException: Input: CALL selectMax
[18:32:04:159962 -04:00] [Thread 31288] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.updateMemberNames does not exist
[18:32:04:159979 -04:00] [Thread 34576] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.deleteOldHouses1 does not exist
[18:32:04:160010 -04:00] [Thread 31288] extDB3: SQL: Error MariaDBQueryException: Input: CALL updateMemberNames
[18:32:04:160032 -04:00] [Thread 34576] extDB3: SQL: Error MariaDBQueryException: Input: CALL deleteOldHouses1
[18:32:04:160712 -04:00] [Thread 32972] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.houseCleanup1 does not exist
[18:32:04:160729 -04:00] [Thread 32972] extDB3: SQL: Error MariaDBQueryException: Input: CALL houseCleanup1
[18:32:04:163799 -04:00] [Thread 32972] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.deleteContracts does not exist
[18:32:04:163826 -04:00] [Thread 34576] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.deleteOldGangs does not exist
[18:32:04:163835 -04:00] [Thread 31288] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.deleteDeadVehicles does not exist
[18:32:04:163979 -04:00] [Thread 29484] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.resetLifeVehicles1 does not exist
[18:32:04:163892 -04:00] [Thread 32972] extDB3: SQL: Error MariaDBQueryException: Input: CALL deleteContracts
[18:32:04:163959 -04:00] [Thread 34576] extDB3: SQL: Error MariaDBQueryException: Input: CALL deleteOldGangs
[18:32:04:163841 -04:00] [Thread 27980] extDB3: SQL: Error MariaDBQueryException: PROCEDURE edenrpdb.giveCash does not exist
[18:32:04:163991 -04:00] [Thread 31288] extDB3: SQL: Error MariaDBQueryException: Input: CALL deleteDeadVehicles
[18:32:04:164046 -04:00] [Thread 29484] extDB3: SQL: Error MariaDBQueryException: Input: CALL resetLifeVehicles1
[18:32:04:164129 -04:00] [Thread 27980] extDB3: SQL: Error MariaDBQueryException: Input: CALL giveCash
[18:32:04:209058 -04:00] [Thread 27980] extDB3: SQL: Error MariaDBQueryException: Unknown column 'houses.expires_on' in 'field list'
[18:32:04:209098 -04:00] [Thread 27980] extDB3: SQL: Error MariaDBQueryException: Input: SELECT houses.id, houses.pid, houses.pos, players.name, houses.player_keys, houses.inventory, houses.storageCapacity, houses.inAH, houses.oil, houses.physical_inventory, houses.physicalStorageCapacity, DATEDIFF(houses.expires_on, TIMESTAMP(CURRENT_DATE())) FROM houses INNER JOIN players ON houses.pid=players.playerid WHERE houses.owned='1' AND server='1' LIMIT 0,10
