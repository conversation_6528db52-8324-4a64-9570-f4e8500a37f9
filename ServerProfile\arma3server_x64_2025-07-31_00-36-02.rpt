=====================================================================
== C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\arma3server_x64.exe
== arma3server_x64.exe  -port=2302 -name="EdenRP_S1" -config=server.cfg -cfg=basic.cfg -profiles=ServerProfile -serverMod="@extDB3;@DiscordEmbedBuilder" -world=Altis -autoInit -loadMissionToMemory -enableHT -hugepages -malloc=system -maxMem=8192 -maxVRAM=2048 -cpuCount=4 -exThreads=7 -bandwidthAlg=2

Original output filename: Arma3Retail_Server_x64
Exe timestamp: 2025/06/24 13:00:10
Current time:  2025/07/31 00:36:03

Type: Public
Build: Stable
Version: 2.20.152984

Allocator: Windows [] []
PhysMem: 32 GiB, VirtMem : 131072 GiB, AvailPhys : 20 GiB, AvailVirt : 131068 GiB, AvailPage : 13 GiB, PageSize : 4.0 KiB/2.0 MiB/HasLockMemory, CPUCount : 4
=====================================================================

 0:36:03 Steam beta branch: none
 0:36:03 Detected number of DLCs: 23
 0:36:03 ---------------------------------------------------------- Game ----------------------------------------------------------
 0:36:03                                                                name      appId   owned  installed  available   isDlc
 0:36:03                                                              Arma 3     107410     yes        yes        yes      no
 0:36:03                                                             Unknown         -1      no         no         no     yes
 0:36:03 ---------------------------------------------------------- Dlcs ----------------------------------------------------------
 0:36:03  index                                                         name      appId   owned  installed  available   isDlc
 0:36:03      0                                                  Arma 3 Maps     249861     yes        yes         no     yes
 0:36:03      1                                        Arma 3 Tactical Guide     249862     yes        yes         no     yes
 0:36:03      2                                                  Arma 3 Zeus     275700     yes        yes        yes     yes
 0:36:03      3                                                 Arma 3 Karts     288520     yes        yes        yes     yes
 0:36:03      4                                           Arma 3 Helicopters     304380     yes        yes        yes     yes
 0:36:03      5                                          Arma 3 DLC Bundle 1     304400     yes        yes         no     yes
 0:36:03      6                                              Arma 3 Marksmen     332350     yes        yes        yes     yes
 0:36:03      7                                                  Arma 3 Apex     395180     yes        yes        yes     yes
 0:36:03      8                                           Arma 3 Laws of War     571710     yes        yes        yes     yes
 0:36:03      9                                                  Arma 3 Jets     601670     yes        yes        yes     yes
 0:36:03     10                                          Arma 3 DLC Bundle 2     612480      no         no         no     yes
 0:36:03     11                                                Arma 3 Malden     639600     yes        yes        yes     yes
 0:36:03     12                                  Arma 3 Tac-Ops Mission Pack     744950      no         no        yes     yes
 0:36:03     13                                                 Arma 3 Tanks     798390      no         no        yes     yes
 0:36:03     14                                               Arma 3 Contact    1021790      no         no        yes     yes
 0:36:03     15   Arma 3 Creator DLC: Global Mobilization - Cold War Germany    1042220      no         no        yes     yes
 0:36:03     16                           Arma 3 Creator DLC: Spearhead 1944    1175380      no         no        yes     yes
 0:36:03     17                      Arma 3 Creator DLC: S.O.G. Prairie Fire    1227700      no         no        yes     yes
 0:36:03     18                        Arma 3 Creator DLC: CSLA Iron Curtain    1294440      no         no        yes     yes
 0:36:03     19                                            Arma 3 Art of War    1325500     yes        yes        yes     yes
 0:36:03     20                           Arma 3 Creator DLC: Western Sahara    1681170      no         no        yes     yes
 0:36:03     21                          Arma 3 Creator DLC: Reaction Forces    2647760      no         no        yes     yes
 0:36:03     22                     Arma 3 Creator DLC: Expeditionary Forces    2647830      no         no        yes     yes
 0:36:03 --------------------------------------------------------------------------------------------------------------------------
 0:36:03 Initializing stats manager.
 0:36:03 Stats config disabled.
 0:36:03 sessionID: 2b549a0a9a9495f1ced52f4bae5a5854c333f98d
 0:36:08 Updating base class RscShortcutButton->RscButton, by a3\editor_f\config.bin/RscDisplayEditObject/Controls/B_OK/ (original bin\config.bin)
 0:36:08 Updating base class RscSliderH->RscXSliderH, by a3\editor_f\config.bin/RscDisplayEditObject/Slider/ (original bin\config.bin)
 0:36:08 Updating base class RscText->RscPicture, by a3\editor_f\config.bin/RscDisplayEditObject/Preview/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButton, by a3\editor_f\config.bin/RscDisplayMissionLoad/Controls/B_OK/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButton, by a3\editor_f\config.bin/RscDisplayMissionSave/Controls/B_OK/ (original bin\config.bin)
 0:36:08 Updating base class ->RscControlsGroup, by a3\ui_f\config.bin/RscControlsGroupNoScrollbars/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscControlsGroup, by a3\ui_f\config.bin/RscControlsGroupNoHScrollbars/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscControlsGroup, by a3\ui_f\config.bin/RscControlsGroupNoVScrollbars/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/RscLine/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscActiveText, by a3\ui_f\config.bin/RscActivePicture/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscButton, by a3\ui_f\config.bin/RscButtonTextOnly/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscShortcutButton, by a3\ui_f\config.bin/RscShortcutButtonMain/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscShortcutButton, by a3\ui_f\config.bin/RscButtonEditor/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscShortcutButton, by a3\ui_f\config.bin/RscIGUIShortcutButton/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscShortcutButton, by a3\ui_f\config.bin/RscGearShortcutButton/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscShortcutButton, by a3\ui_f\config.bin/RscButtonMenu/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscButtonMenu, by a3\ui_f\config.bin/RscButtonMenuOK/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscButtonMenu, by a3\ui_f\config.bin/RscButtonMenuCancel/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscButtonMenu, by a3\ui_f\config.bin/RscButtonMenuSteam/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/RscLoadingText/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscListBox, by a3\ui_f\config.bin/RscIGUIListBox/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscListNBox, by a3\ui_f\config.bin/RscIGUIListNBox/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/RscBackground/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/RscBackgroundGUI/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscPicture, by a3\ui_f\config.bin/RscBackgroundGUILeft/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscPicture, by a3\ui_f\config.bin/RscBackgroundGUIRight/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscPicture, by a3\ui_f\config.bin/RscBackgroundGUIBottom/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/RscBackgroundGUITop/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/RscBackgroundGUIDark/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscPictureKeepAspect, by a3\ui_f\config.bin/RscBackgroundLogo/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscMapControl, by a3\ui_f\config.bin/RscMapControlEmpty/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscPicture, by a3\ui_f\config.bin/CA_Mainback/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->CA_Mainback, by a3\ui_f\config.bin/CA_Back/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->CA_Mainback, by a3\ui_f\config.bin/CA_Title_Back/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->CA_Mainback, by a3\ui_f\config.bin/CA_Black_Back/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscTitle, by a3\ui_f\config.bin/CA_Title/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscPictureKeepAspect, by a3\ui_f\config.bin/CA_Logo/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->CA_Logo, by a3\ui_f\config.bin/CA_Logo_Small/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscButton, by a3\ui_f\config.bin/CA_RscButton/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->CA_RscButton, by a3\ui_f\config.bin/CA_RscButton_dialog/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscActiveText, by a3\ui_f\config.bin/CA_Ok/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/CA_Ok_image/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/CA_Ok_image2/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/CA_Ok_text/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscPicture, by a3\ui_f\config.bin/RscVignette/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->RscControlsGroupNoScrollbars, by a3\ui_f\config.bin/RscMapControlTooltip/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class RscUnitInfo->RscUnitInfoAirNoWeapon, by a3\ui_f\config.bin/RscInGameUI/RscUnitInfoAir/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class RscControlsGroup->RscControlsGroupNoScrollbars, by a3\ui_f\config.bin/RscInGameUI/RscTaskOverview/controls/TaskOverviewAssigned/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenu, by a3\ui_f\config.bin/RscDisplayDebug/Controls/B_OK/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenu, by a3\ui_f\config.bin/RscDisplayDebug/Controls/B_Cancel/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenu, by a3\ui_f\config.bin/RscDisplayDebug/Controls/B_Clear/ (original bin\config.bin)
 0:36:08 Updating base class ->RscText, by a3\ui_f\config.bin/RscDisplayCapture/controls/TimeLines/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenu, by a3\ui_f\config.bin/RscDisplayCapture/controls/ButtonAverages/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenu, by a3\ui_f\config.bin/RscDisplayCapture/controls/ButtonSavePreviousData/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenu, by a3\ui_f\config.bin/RscDisplayCapture/controls/ButtonPreviousData/ (original bin\config.bin)
 0:36:08 Updating base class RscPicture->RscPictureKeepAspect, by a3\ui_f\config.bin/RscDisplayMain/IconPicture/ (original bin\config.bin)
 0:36:08 Updating base class IconPicture->RscPictureKeepAspect, by a3\ui_f\config.bin/RscDisplayMain/DlcOwnedIconPicture/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class IconPicture->RscPictureKeepAspect, by a3\ui_f\config.bin/RscDisplayMain/DlcIconPicture/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class RscControlsGroup->RscControlsGroupNoScrollbars, by a3\ui_f\config.bin/RscDisplayCampaignLoad/controls/OverviewGroup/ (original bin\config.bin)
 0:36:08 Updating base class RscButton->RscButtonSearch, by a3\ui_f\config.bin/RscDisplayCampaignLoad/controls/SearchButton/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenuCancel, by a3\ui_f\config.bin/RscDisplayCampaignLoad/controls/ButtonCancel/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenu, by a3\ui_f\config.bin/RscDisplayCampaignLoad/controls/ButtonGameOptions/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenuSteam, by a3\ui_f\config.bin/RscDisplayCampaignLoad/controls/ButtonBuyDLC/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenu, by a3\ui_f\config.bin/RscDisplayCampaignLoad/controls/ButtonRevert/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenuOK, by a3\ui_f\config.bin/RscDisplayCampaignLoad/controls/ButtonOK/ (original bin\config.bin)
 0:36:08 Updating base class RscListBox->RscCombo, by a3\ui_f\config.bin/RscDisplayCustomizeController/Steepness/ (original bin\config.bin)
 0:36:08 Updating base class ->RscStandardDisplay, by a3\ui_f\config.bin/RscDisplayControlSchemes/ (original bin\config.bin)
 0:36:08 Updating base class ButtonOK->RscButtonMenuCancel, by a3\ui_f\config.bin/RscDisplayControlSchemes/controls/ButtonCancel/ (original bin\config.bin)
 0:36:08 Updating base class RscButton->RscButtonMenuOK, by a3\ui_f\config.bin/RscDisplayControlSchemes/controls/ButtonOK/ (original bin\config.bin)
 0:36:08 Updating base class RscPicture->RscPictureKeepAspect, by a3\ui_f\config.bin/RscDisplayFileSelectImage/controls/OverviewPicture/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenuCancel, by a3\ui_f\config.bin/RscDisplayFieldManual/Controls/ButtonCancel/ (original bin\config.bin)
 0:36:08 Cannot delete class B_KickOff, it is referenced somewhere (used as a base class probably).
 0:36:08 Updating base class RscButton->RscButtonMenuCancel, by a3\ui_f\config.bin/RscDisplayPublishMission/controls/ButtonCancel/ (original bin\config.bin)
 0:36:08 Updating base class RscShortcutButton->RscButtonMenuOK, by a3\ui_f\config.bin/RscDisplayPublishMissionSelectTags/controls/ButtonOK/ (original bin\config.bin)
 0:36:08 Updating base class ButtonOK->RscButtonMenuCancel, by a3\ui_f\config.bin/RscDisplayPublishMissionSelectTags/controls/ButtonCancel/ (original bin\config.bin)
 0:36:08 Updating base class ->RscSubmenu, by a3\ui_f\config.bin/RscMainMenu/ (original bin\config.bin)
 0:36:08 Updating base class ->DistanceClose, by a3\ui_f\config.bin/CfgSimpleTasks/Icon3D/DistanceMid/ (original bin\config.bin)
 0:36:08 Updating base class ->DistanceClose, by a3\ui_f\config.bin/CfgSimpleTasks/Icon3D/DistanceLong/ (original bin\config.bin)
 0:36:08 Updating base class ->ctrlDefaultText, by a3\3den\config.bin/ctrlStatic/ (original a3\3den\config.bin)
 0:36:08 Updating base class ->ctrlActiveText, by a3\3den\config.bin/ctrlActivePicture/ (original a3\3den\config.bin)
 0:36:08 Updating base class ->ctrlDefaultText, by a3\3den\config.bin/ctrlStructuredText/ (original a3\3den\config.bin)
 0:36:08 Updating base class ->ctrlControlsGroup, by a3\3den\config.bin/ctrlControlsGroupNoScrollbars/ (original a3\3den\config.bin)
 0:36:08 Updating base class ->ctrlDefault, by a3\3den\config.bin/ctrlCheckbox/ (original a3\3den\config.bin)
 0:36:08 Updating base class ->ctrlCheckbox, by a3\3den\config.bin/ctrlCheckboxBaseline/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisable, by a3\3den\config.bin/RscDisplayOptionsAudio/ControlsBackground/BackgroundDisable/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisableTiles, by a3\3den\config.bin/RscDisplayOptionsAudio/ControlsBackground/BackgroundDisableTiles/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisable, by a3\3den\config.bin/RscDisplayConfigure/ControlsBackground/BackgroundDisable/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisableTiles, by a3\3den\config.bin/RscDisplayConfigure/ControlsBackground/BackgroundDisableTiles/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisable, by a3\3den\config.bin/RscDisplayConfigureAction/ControlsBackground/BackgroundDisable/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisableTiles, by a3\3den\config.bin/RscDisplayConfigureAction/ControlsBackground/BackgroundDisableTiles/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisable, by a3\3den\config.bin/RscDisplayConfigureControllers/ControlsBackground/BackgroundDisable/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisableTiles, by a3\3den\config.bin/RscDisplayConfigureControllers/ControlsBackground/BackgroundDisableTiles/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisable, by a3\3den\config.bin/RscDisplayGameOptions/ControlsBackground/BackgroundDisable/ (original a3\3den\config.bin)
 0:36:08 Updating base class RscText->ctrlStaticBackgroundDisableTiles, by a3\3den\config.bin/RscDisplayGameOptions/ControlsBackground/BackgroundDisableTiles/ (original a3\3den\config.bin)
 0:36:08 Updating base class controls->, by a3\3den\config.bin/RscDisplayArcadeMap_Layout_2/Controls/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class controls->, by a3\3den\config.bin/RscDisplayArcadeMap_Layout_6/Controls/ (original a3\ui_f\config.bin)
 0:36:08 Updating base class ->ctrlControlsGroupNoScrollbars, by a3\3den\config.bin/Cfg3DEN/Attributes/Default/ (original a3\3den\config.bin)
 0:36:08 Updating base class ->ctrlStatic, by a3\3den\config.bin/Cfg3DEN/Attributes/Title/Controls/Title/ (original a3\3den\config.bin)
 0:36:08 Updating base class ->ctrlToolbox, by a3\3den\config.bin/Cfg3DEN/Attributes/Toolbox/Controls/Value/ (original a3\3den\config.bin)
 0:36:09 Updating base class Mod_Base->, by a3\data_f_destroyer\config.bin/CfgMods/Jets/ (original a3\data_f_jets\config.bin)
 0:36:10 Cannot open ServerProfile\OfficialServersCache\list.json
 0:36:10 Canceling the list loading process, the load of the file failed: ServerProfile\OfficialServersCache\list.json
 0:36:10 Initializing Steam Manager
 0:36:10 Starting initial content check.
 0:36:10 Steam Manager initialized.
 0:36:10 
 0:36:10 ==== Loaded addons ====
 0:36:10 
 0:36:10 dta\bin.pbo - 152984
 0:36:10 dta\core.pbo - 129618
 0:36:10 dta\languagecore_f.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\anims_f_aow.pbo - 149768
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\characters_f_aow.pbo - 150659
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\data_f_aow.pbo - 149928
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\dubbing_f_aow.pbo - 149901
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\editorpreviews_f_aow.pbo - 149909
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\functions_f_aow.pbo - 149593
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\languagemissions_f_aow.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\language_f_aow.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\missions_f_aow.pbo - 150974
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\missions_f_aow_data.pbo - 150984
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\missions_f_aow_video.pbo - 149837
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\props_f_aow.pbo - 150912
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\sounds_f_aow.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\structures_f_aow.pbo - 149883
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\supplies_f_aow.pbo - 150149
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow\addons\ui_f_aow.pbo - 149917
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\air_f_enoch.pbo - 145904
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\anims_f_enoch.pbo - 150341
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\armor_f_enoch.pbo - 145904
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\cargoposes_f_enoch.pbo - 142478
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\characters_f_enoch.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\data_f_enoch.pbo - 150070
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\dubbing_radio_f_enoch.pbo - 150070
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\dubbing_radio_f_enoch_data.pbo - 150020
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\editorpreviews_f_enoch.pbo - 150962
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\functions_f_enoch.pbo - 148356
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\languagemissions_f_enoch.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\language_f_enoch.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\map_enoch.pbo - 150292
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\map_enoch_data.pbo - 145274
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\map_enoch_data_layers.pbo - 146184
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\map_enoch_scenes_f.pbo - 143336
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\missions_f_enoch.pbo - 149487
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\music_f_enoch.pbo - 147472
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\music_f_enoch_music.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\props_f_enoch.pbo - 150309
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\rocks_f_enoch.pbo - 148432
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\soft_f_enoch.pbo - 150885
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\sounds_f_enoch.pbo - 150842
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\static_f_enoch.pbo - 145904
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\structures_f_enoch.pbo - 148551
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\structures_f_enoch_civilian.pbo - 150926
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\structures_f_enoch_commercial.pbo - 148419
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\structures_f_enoch_cultural.pbo - 150415
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\structures_f_enoch_data.pbo - 148419
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\structures_f_enoch_furniture.pbo - 148419
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\structures_f_enoch_industrial.pbo - 150920
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\structures_f_enoch_infrastructure.pbo - 148551
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\structures_f_enoch_military.pbo - 150174
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\supplies_f_enoch.pbo - 151084
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\ui_f_enoch.pbo - 147095
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\vegetation_f_enoch.pbo - 150192
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch\addons\weapons_f_enoch.pbo - 150671
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\armor_f_tank.pbo - 150292
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\cargoposes_f_tank.pbo - 128283
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\characters_f_tank.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\data_f_tank.pbo - 128203
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\dubbing_f_tank.pbo - 127958
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\editorpreviews_f_tank.pbo - 137875
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\functions_f_tank.pbo - 125996
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\languagemissions_f_tank.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\language_f_tank.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\missions_f_tank.pbo - 150984
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\missions_f_tank_data.pbo - 128937
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\modules_f_tank.pbo - 147857
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\music_f_tank.pbo - 127912
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\props_f_tank.pbo - 131702
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\sounds_f_tank.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\structures_f_tank.pbo - 150249
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\ui_f_tank.pbo - 148551
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank\addons\weapons_f_tank.pbo - 151040
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\characters_f_tacops.pbo - 129739
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\data_f_tacops.pbo - 150102
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\dubbing_f_tacops.pbo - 129340
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\functions_f_tacops.pbo - 150415
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\languagemissions_f_tacops.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\language_f_tacops.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\missions_f_tacops.pbo - 144976
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\modules_f_tacops.pbo - 150149
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\music_f_tacops.pbo - 124064
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\sounds_f_tacops.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops\addons\ui_f_tacops.pbo - 148551
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\air_f_orange.pbo - 150970
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\cargoposes_f_orange.pbo - 126225
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\characters_f_orange.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\data_f_orange.pbo - 144284
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\dubbing_f_orange.pbo - 121689
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\editorpreviews_f_orange.pbo - 123173
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\functions_f_orange.pbo - 150945
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\languagemissions_f_orange.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\language_f_orange.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\missions_f_orange.pbo - 150364
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\modules_f_orange.pbo - 143632
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\music_f_orange.pbo - 120725
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\props_f_orange.pbo - 148864
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\soft_f_orange.pbo - 150945
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\sounds_f_orange.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\structures_f_orange.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\supplies_f_orange.pbo - 129371
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\ui_f_orange.pbo - 151010
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange\addons\weapons_f_orange.pbo - 150671
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\armor_f_argo.pbo - 129739
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\characters_f_patrol.pbo - 149127
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\data_f_argo.pbo - 128209
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\data_f_patrol.pbo - 141307
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\editorpreviews_f_argo.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\functions_f_patrol.pbo - 141510
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\languagemissions_f_patrol.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\language_f_argo.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\language_f_patrol.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\map_malden.pbo - 150292
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\map_malden_data.pbo - 135888
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\map_malden_data_layers.pbo - 135888
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\map_malden_scenes_f.pbo - 120026
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\missions_f_patrol.pbo - 126663
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\modules_f_patrol.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\music_f_argo.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\props_f_argo.pbo - 129371
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\rocks_f_argo.pbo - 148432
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\sounds_f_patrol.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\structures_f_argo.pbo - 150961
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\ui_f_patrol.pbo - 141307
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\vegetation_f_argo.pbo - 148432
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo\addons\weapons_f_patrol.pbo - 141307
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\air_f_jets.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\anims_f_jets.pbo - 123210
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\boat_f_destroyer.pbo - 143297
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\boat_f_jets.pbo - 150071
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\cargoposes_f_jets.pbo - 126225
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\characters_f_jets.pbo - 129739
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\data_f_destroyer.pbo - 150370
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\data_f_jets.pbo - 150370
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\data_f_sams.pbo - 150370
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\dubbing_f_jets.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\editorpreviews_f_destroyer.pbo - 132141
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\editorpreviews_f_jets.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\editorpreviews_f_sams.pbo - 132141
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\functions_f_destroyer.pbo - 150071
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\functions_f_jets.pbo - 151077
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\languagemissions_f_jets.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\language_f_destroyer.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\language_f_jets.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\language_f_sams.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\missions_f_jets.pbo - 130755
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\modules_f_jets.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\music_f_jets.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\props_f_destroyer.pbo - 132141
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\props_f_jets.pbo - 129371
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\sounds_f_jets.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\static_f_destroyer.pbo - 140243
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\static_f_jets.pbo - 146744
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\static_f_sams.pbo - 146744
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\ui_f_jets.pbo - 126137
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\weapons_f_destroyer.pbo - 132268
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\weapons_f_jets.pbo - 148847
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets\addons\weapons_f_sams.pbo - 149414
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\air_f_exp.pbo - 150885
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\anims_f_exp.pbo - 126506
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\armor_f_exp.pbo - 141671
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\boat_f_exp.pbo - 150071
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\cargoposes_f_exp.pbo - 126224
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\characters_f_exp.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\characters_f_oldman.pbo - 148698
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\data_f_exp.pbo - 148936
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\data_f_oldman.pbo - 148580
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\dubbing_f_exp.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\dubbing_f_oldman.pbo - 148756
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\dubbing_radio_f_exp.pbo - 119458
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\dubbing_radio_f_exp_data_chi.pbo - 119458
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\dubbing_radio_f_exp_data_engfre.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\dubbing_radio_f_exp_data_fre.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\editorpreviews_f_exp.pbo - 150961
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\editorpreviews_f_oldman.pbo - 150961
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\functions_f_exp.pbo - 144006
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\functions_f_oldman.pbo - 148553
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\languagemissions_f_exp.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\languagemissions_f_oldman.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\language_f_exp.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\language_f_oldman.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\map_data_exp.pbo - 136265
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\map_tanoabuka.pbo - 150292
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\map_tanoabuka_data.pbo - 135884
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\map_tanoabuka_data_layers.pbo - 121192
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\map_tanoabuka_data_layers_00_00.pbo - 121192
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\map_tanoa_scenes_f.pbo - 123527
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\missions_f_exp.pbo - 144315
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\missions_f_exp_data.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\missions_f_exp_video.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\missions_f_oldman.pbo - 151155
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\modules_f_exp.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\modules_f_oldman.pbo - 148553
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\music_f_exp.pbo - 119477
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\music_f_exp_music.pbo - 119477
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\music_f_oldman.pbo - 148553
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\music_f_oldman_music.pbo - 148679
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\props_f_exp.pbo - 147637
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\props_f_oldman.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\rocks_f_exp.pbo - 148432
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\soft_f_exp.pbo - 150945
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\soft_f_oldman.pbo - 148580
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\sounds_f_exp.pbo - 150920
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\sounds_f_oldman.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\static_f_exp.pbo - 119478
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\static_f_oldman.pbo - 148580
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\structures_f_exp.pbo - 150912
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\structures_f_exp_civilian.pbo - 148739
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\structures_f_exp_commercial.pbo - 148419
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\structures_f_exp_cultural.pbo - 148419
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\structures_f_exp_data.pbo - 148419
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\structures_f_exp_industrial.pbo - 148419
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\structures_f_exp_infrastructure.pbo - 148551
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\structures_f_oldman.pbo - 150089
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\supplies_f_exp.pbo - 151084
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\supplies_f_oldman.pbo - 148553
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\ui_f_exp.pbo - 123398
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\ui_f_oldman.pbo - 149216
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\vegetation_f_exp.pbo - 148432
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion\addons\weapons_f_exp.pbo - 150671
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\anims_f_mark.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\characters_f_mark.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\data_f_mark.pbo - 129371
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\dubbing_f_mark.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\dubbing_f_mp_mark.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\functions_f_mark.pbo - 150934
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\functions_f_mp_mark.pbo - 150802
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\languagemissions_f_mark.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\languagemissions_f_mp_mark.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\language_f_mark.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\language_f_mp_mark.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\missions_f_mark.pbo - 122301
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\missions_f_mark_data.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\missions_f_mark_video.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\missions_f_mp_mark.pbo - 141704
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\missions_f_mp_mark_data.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\modules_f_mark.pbo - 148886
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\modules_f_mp_mark.pbo - 150251
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\music_f_mark.pbo - 119477
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\music_f_mark_music.pbo - 119477
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\sounds_f_mark.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\static_f_mark.pbo - 146756
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\structures_f_mark.pbo - 149003
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\supplies_f_mark.pbo - 140562
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\ui_f_mark.pbo - 119478
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\ui_f_mp_mark.pbo - 119478
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark\addons\weapons_f_mark.pbo - 149339
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\air_f_heli.pbo - 150885
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\anims_f_heli.pbo - 123210
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\boat_f_heli.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\cargoposes_f_heli.pbo - 126231
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\data_f_heli.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\dubbing_f_heli.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\functions_f_heli.pbo - 123022
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\languagemissions_f_heli.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\language_f_heli.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\missions_f_heli.pbo - 151010
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\missions_f_heli_data.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\missions_f_heli_video.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\modules_f_heli.pbo - 150249
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\music_f_heli.pbo - 119477
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\music_f_heli_music.pbo - 119477
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\soft_f_heli.pbo - 129742
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\sounds_f_heli.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\structures_f_heli.pbo - 141968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\supplies_f_heli.pbo - 145695
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli\addons\ui_f_heli.pbo - 119478
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\anims_f_kart.pbo - 123210
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\characters_f_kart.pbo - 150968
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\data_f_kart.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\languagemissions_f_kart.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\language_f_kart.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\missions_f_kart.pbo - 149274
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\missions_f_kart_data.pbo - 119459
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\modules_f_kart.pbo - 151162
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\modules_f_kart_data.pbo - 149274
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\soft_f_kart.pbo - 150945
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\sounds_f_kart.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\structures_f_kart.pbo - 123419
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\ui_f_kart.pbo - 119478
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart\addons\weapons_f_kart.pbo - 138461
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\curator\addons\data_f_curator.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\curator\addons\data_f_curator_music.pbo - 119457
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\curator\addons\functions_f_curator.pbo - 150945
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\curator\addons\language_f_curator.pbo - 151164
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\curator\addons\missions_f_curator.pbo - 121570
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\curator\addons\modules_f_curator.pbo - 151084
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\curator\addons\ui_f_curator.pbo - 150867
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\@DiscordEmbedBuilder\addons\cau.discordembedbuilder.pbo - Sat 14 Jan 2023, 08:41:21 AM (UTC+10)
 0:36:10 C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\@extDB3\addons\extdb3.pbo - unknown
 0:36:10 addons\3den.pbo - 151061
 0:36:10 addons\3den_language.pbo - 151164
 0:36:10 addons\a3.pbo - unknown
 0:36:10 addons\air_f.pbo - 150885
 0:36:10 addons\air_f_beta.pbo - 151175
 0:36:10 addons\air_f_epb.pbo - 150885
 0:36:10 addons\air_f_epc.pbo - 150048
 0:36:10 addons\air_f_gamma.pbo - 150048
 0:36:10 addons\animals_f.pbo - 150071
 0:36:10 addons\animals_f_beta.pbo - 150071
 0:36:10 addons\anims_f.pbo - 150686
 0:36:10 addons\anims_f_bootcamp.pbo - 123209
 0:36:10 addons\anims_f_data.pbo - 148601
 0:36:10 addons\anims_f_epa.pbo - 134499
 0:36:10 addons\anims_f_epc.pbo - 121358
 0:36:10 addons\anims_f_exp_a.pbo - 123209
 0:36:10 addons\anims_f_mod.pbo - 135280
 0:36:10 addons\armor_f.pbo - 149414
 0:36:10 addons\armor_f_beta.pbo - 150339
 0:36:10 addons\armor_f_decade.pbo - 150805
 0:36:10 addons\armor_f_epb.pbo - 150260
 0:36:10 addons\armor_f_epc.pbo - 148819
 0:36:10 addons\armor_f_gamma.pbo - 150803
 0:36:10 addons\baseconfig_f.pbo - 119457
 0:36:10 addons\boat_f.pbo - 150265
 0:36:10 addons\boat_f_beta.pbo - 132036
 0:36:10 addons\boat_f_epc.pbo - 131911
 0:36:10 addons\boat_f_gamma.pbo - 127529
 0:36:10 addons\cargoposes_f.pbo - 149489
 0:36:10 addons\characters_f.pbo - 151039
 0:36:10 addons\characters_f_beta.pbo - 149965
 0:36:10 addons\characters_f_bootcamp.pbo - 149274
 0:36:10 addons\characters_f_decade.pbo - 150784
 0:36:10 addons\characters_f_epa.pbo - 148392
 0:36:10 addons\characters_f_epb.pbo - 150968
 0:36:10 addons\characters_f_epc.pbo - 150968
 0:36:10 addons\characters_f_gamma.pbo - 150968
 0:36:10 addons\data_f.pbo - 151162
 0:36:10 addons\data_f_bootcamp.pbo - 129618
 0:36:10 addons\data_f_decade.pbo - 150764
 0:36:10 addons\data_f_exp_a.pbo - 119457
 0:36:10 addons\data_f_exp_b.pbo - 119457
 0:36:10 addons\data_f_mod.pbo - 131534
 0:36:10 addons\data_f_warlords.pbo - 150867
 0:36:10 addons\drones_f.pbo - 146855
 0:36:10 addons\dubbing_f.pbo - 119457
 0:36:10 addons\dubbing_f_beta.pbo - 119457
 0:36:10 addons\dubbing_f_bootcamp.pbo - 119457
 0:36:10 addons\dubbing_f_epa.pbo - 119457
 0:36:10 addons\dubbing_f_epb.pbo - 119457
 0:36:10 addons\dubbing_f_epc.pbo - 119457
 0:36:10 addons\dubbing_f_gamma.pbo - 122449
 0:36:10 addons\dubbing_f_warlords.pbo - 136700
 0:36:10 addons\dubbing_radio_f.pbo - 119457
 0:36:10 addons\dubbing_radio_f_data_eng.pbo - 119457
 0:36:10 addons\dubbing_radio_f_data_engb.pbo - 119458
 0:36:10 addons\dubbing_radio_f_data_gre.pbo - 119458
 0:36:10 addons\dubbing_radio_f_data_per.pbo - 150218
 0:36:10 addons\dubbing_radio_f_data_vr.pbo - 119458
 0:36:10 addons\editorpreviews_f.pbo - 151077
 0:36:10 addons\editorpreviews_f_decade.pbo - 150645
 0:36:10 addons\editor_f.pbo - 121103
 0:36:10 addons\functions_f.pbo - 151080
 0:36:10 addons\functions_f_bootcamp.pbo - 151040
 0:36:10 addons\functions_f_decade.pbo - 150807
 0:36:10 addons\functions_f_epa.pbo - 150249
 0:36:10 addons\functions_f_epc.pbo - 119458
 0:36:10 addons\functions_f_exp_a.pbo - 150958
 0:36:10 addons\functions_f_warlords.pbo - 150354
 0:36:10 addons\languagemissions_f.pbo - 151164
 0:36:10 addons\languagemissions_f_beta.pbo - 151164
 0:36:10 addons\languagemissions_f_bootcamp.pbo - 151164
 0:36:10 addons\languagemissions_f_epa.pbo - 151164
 0:36:10 addons\languagemissions_f_epb.pbo - 151164
 0:36:10 addons\languagemissions_f_epc.pbo - 151164
 0:36:10 addons\languagemissions_f_exp_a.pbo - 151164
 0:36:10 addons\languagemissions_f_gamma.pbo - 151164
 0:36:10 addons\language_f.pbo - 151164
 0:36:10 addons\language_f_beta.pbo - 151164
 0:36:10 addons\language_f_bootcamp.pbo - 151164
 0:36:10 addons\language_f_decade.pbo - 151164
 0:36:10 addons\language_f_epa.pbo - 151164
 0:36:10 addons\language_f_epb.pbo - 151164
 0:36:10 addons\language_f_epc.pbo - 151164
 0:36:10 addons\language_f_exp_a.pbo - 151164
 0:36:10 addons\language_f_exp_b.pbo - 151164
 0:36:10 addons\language_f_gamma.pbo - 151164
 0:36:10 addons\language_f_mod.pbo - 151164
 0:36:10 addons\language_f_warlords.pbo - 151164
 0:36:10 addons\map_altis.pbo - 148442
 0:36:10 addons\map_altis_data.pbo - 135904
 0:36:10 addons\map_altis_data_layers.pbo - 135913
 0:36:10 addons\map_altis_data_layers_00_00.pbo - 0000
 0:36:10 addons\map_altis_data_layers_00_01.pbo - 0000
 0:36:10 addons\map_altis_data_layers_01_00.pbo - 0000
 0:36:10 addons\map_altis_data_layers_01_01.pbo - 0000
 0:36:10 addons\map_altis_scenes_f.pbo - 119459
 0:36:10 addons\map_data.pbo - 134124
 0:36:10 addons\map_stratis.pbo - 148442
 0:36:10 addons\map_stratis_data.pbo - 135888
 0:36:10 addons\map_stratis_data_layers.pbo - 135888
 0:36:10 addons\map_stratis_scenes_f.pbo - 119459
 0:36:10 addons\map_vr.pbo - 135900
 0:36:10 addons\map_vr_scenes_f.pbo - 119459
 0:36:10 addons\misc_f.pbo - 119459
 0:36:10 addons\missions_f.pbo - 141704
 0:36:10 addons\missions_f_beta.pbo - 150697
 0:36:10 addons\missions_f_beta_data.pbo - 150867
 0:36:10 addons\missions_f_beta_video.pbo - 119459
 0:36:10 addons\missions_f_bootcamp.pbo - 145509
 0:36:10 addons\missions_f_bootcamp_data.pbo - 150867
 0:36:10 addons\missions_f_bootcamp_video.pbo - 119459
 0:36:10 addons\missions_f_data.pbo - 119459
 0:36:10 addons\missions_f_epa.pbo - 150459
 0:36:10 addons\missions_f_epa_data.pbo - 119459
 0:36:10 addons\missions_f_epa_video.pbo - 119459
 0:36:10 addons\missions_f_epb.pbo - 119459
 0:36:10 addons\missions_f_epc.pbo - 141704
 0:36:10 addons\missions_f_exp_a.pbo - 141704
 0:36:10 addons\missions_f_exp_a_data.pbo - 119459
 0:36:10 addons\missions_f_gamma.pbo - 151061
 0:36:10 addons\missions_f_gamma_data.pbo - 119459
 0:36:10 addons\missions_f_gamma_video.pbo - 119459
 0:36:10 addons\missions_f_video.pbo - 119459
 0:36:10 addons\missions_f_warlords.pbo - 145437
 0:36:10 addons\modules_f.pbo - 150958
 0:36:10 addons\modules_f_beta.pbo - 151162
 0:36:10 addons\modules_f_beta_data.pbo - 149274
 0:36:10 addons\modules_f_bootcamp.pbo - 119459
 0:36:10 addons\modules_f_data.pbo - 122470
 0:36:10 addons\modules_f_epb.pbo - 134655
 0:36:10 addons\modules_f_exp_a.pbo - 119459
 0:36:10 addons\modules_f_warlords.pbo - 144205
 0:36:10 addons\music_f.pbo - 119459
 0:36:10 addons\music_f_bootcamp.pbo - 119459
 0:36:10 addons\music_f_bootcamp_music.pbo - 119459
 0:36:10 addons\music_f_epa.pbo - 119459
 0:36:10 addons\music_f_epa_music.pbo - 119459
 0:36:10 addons\music_f_epb.pbo - 119459
 0:36:10 addons\music_f_epb_music.pbo - 119459
 0:36:10 addons\music_f_epc.pbo - 119459
 0:36:10 addons\music_f_epc_music.pbo - 119477
 0:36:10 addons\music_f_music.pbo - 119477
 0:36:10 addons\plants_f.pbo - 148432
 0:36:10 addons\props_f_decade.pbo - 150920
 0:36:10 addons\props_f_exp_a.pbo - 147637
 0:36:10 addons\roads_f.pbo - 148432
 0:36:10 addons\rocks_f.pbo - 149339
 0:36:10 addons\signs_f.pbo - 148432
 0:36:10 addons\soft_f.pbo - 151061
 0:36:10 addons\soft_f_beta.pbo - 150920
 0:36:10 addons\soft_f_bootcamp.pbo - 150071
 0:36:10 addons\soft_f_epc.pbo - 150070
 0:36:10 addons\soft_f_gamma.pbo - 150920
 0:36:10 addons\sounds_f.pbo - 150927
 0:36:10 addons\sounds_f_arsenal.pbo - 150867
 0:36:10 addons\sounds_f_bootcamp.pbo - 150842
 0:36:10 addons\sounds_f_characters.pbo - 150867
 0:36:10 addons\sounds_f_decade.pbo - 150867
 0:36:10 addons\sounds_f_environment.pbo - 150867
 0:36:10 addons\sounds_f_epb.pbo - 150867
 0:36:10 addons\sounds_f_epc.pbo - 150867
 0:36:10 addons\sounds_f_exp_a.pbo - 150867
 0:36:10 addons\sounds_f_mod.pbo - 150867
 0:36:10 addons\sounds_f_sfx.pbo - 150867
 0:36:10 addons\sounds_f_vehicles.pbo - 150867
 0:36:10 addons\static_f.pbo - 151061
 0:36:10 addons\static_f_beta.pbo - 132365
 0:36:10 addons\static_f_gamma.pbo - 122615
 0:36:10 addons\structures_f.pbo - 150697
 0:36:10 addons\structures_f_bootcamp.pbo - 143436
 0:36:10 addons\structures_f_data.pbo - 148419
 0:36:10 addons\structures_f_epa.pbo - 149252
 0:36:10 addons\structures_f_epb.pbo - 148421
 0:36:10 addons\structures_f_epc.pbo - 148804
 0:36:10 addons\structures_f_exp_a.pbo - 150912
 0:36:10 addons\structures_f_households.pbo - 150926
 0:36:10 addons\structures_f_ind.pbo - 150048
 0:36:10 addons\structures_f_mil.pbo - 150697
 0:36:10 addons\structures_f_wrecks.pbo - 148419
 0:36:10 addons\uifonts_f.pbo - 119478
 0:36:10 addons\uifonts_f_data.pbo - 150106
 0:36:10 addons\ui_f.pbo - 151173
 0:36:10 addons\ui_f_bootcamp.pbo - 119478
 0:36:10 addons\ui_f_data.pbo - 151023
 0:36:10 addons\ui_f_decade.pbo - 150746
 0:36:10 addons\ui_f_exp_a.pbo - 119478
 0:36:10 addons\weapons_f.pbo - 151176
 0:36:10 addons\weapons_f_beta.pbo - 150174
 0:36:10 addons\weapons_f_bootcamp.pbo - 141713
 0:36:10 addons\weapons_f_decade.pbo - 150757
 0:36:10 addons\weapons_f_epa.pbo - 150905
 0:36:10 addons\weapons_f_epb.pbo - 137810
 0:36:10 addons\weapons_f_epc.pbo - 130416
 0:36:10 addons\weapons_f_gamma.pbo - 148783
 0:36:10 addons\weapons_f_mod.pbo - 149339
 0:36:10 
 0:36:10 =======================
 0:36:10 
 0:36:10 ============================================================================================= List of mods ===============================================================================================
 0:36:10 modsReadOnly = false
 0:36:10 safeModsActivated = false
 0:36:10 customMods = true
 0:36:10 hash = '90BDCFA91BC424BAD38183AEF2610A6703E12178'
 0:36:10 hashShort = 'd45fd29e'
 0:36:10                                               name |               modDir |    default |   official |               origin |                                     hash | hashShort | fullPath
 0:36:10 ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 0:36:10                                  Arma 3 Art of War |                  aow |       true |       true |             GAME DIR | 7685ba6030e4deb3c55cc1303fe4970086bb1d4e |  45cead90 | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\aow
 0:36:10                          Arma 3 Contact (Platform) |                enoch |       true |       true |             GAME DIR | 60377b76eed5b3b0aa32b7eac60a47d283fcf0cc |  b428902f | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\enoch
 0:36:10                                       Arma 3 Tanks |                 tank |       true |       true |             GAME DIR | ce4faa4b481d5beb49a49be538691d35e51771e2 |  6e220316 | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tank
 0:36:10                                     Arma 3 Tac-Ops |               tacops |       true |       true |             GAME DIR | ae5caba2339455413681ee7a0d13d6c3506c012c |  5150d064 | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\tacops
 0:36:10                                 Arma 3 Laws of War |               orange |       true |       true |             GAME DIR | 842d91a189db46f5c940e3fecb475854965c87f7 |  107dffec | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\orange
 0:36:10                                      Arma 3 Malden |                 argo |       true |       true |             GAME DIR | 6929f5532e7b80136be17f26a959c91eeb40ec0f |  aad6aaf5 | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\argo
 0:36:10                                        Arma 3 Jets |                 jets |       true |       true |             GAME DIR | e4a48f3a8727e6b56d6082c1dd0b4ab3c42be223 |  7f335115 | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\jets
 0:36:10                                        Arma 3 Apex |            expansion |       true |       true |             GAME DIR | 8e9eb113e1e92a3ba26280becd3f05e7bdfcd49c |  125ee13c | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\expansion
 0:36:10                                    Arma 3 Marksmen |                 mark |       true |       true |             GAME DIR | 392d9e3a6fdbc19f01be0200b50a6a09413ecc63 |  140e9f85 | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mark
 0:36:10                                 Arma 3 Helicopters |                 heli |       true |       true |             GAME DIR | 0e67ef03a2e786a8e11e01c4b19cd104e120f282 |  61c33e7e | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\heli
 0:36:10                                       Arma 3 Karts |                 kart |       true |       true |             GAME DIR | 0ab891520b9ac69ff9132ae6de7e2e199b884ca6 |  f6336162 | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\kart
 0:36:10                                        Arma 3 Zeus |              curator |       true |       true |             GAME DIR | 53c3d7094eaa421dad414709f2834c660b583ad3 |  3b763ef8 | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\curator
 0:36:10                                             Arma 3 |                   A3 |       true |       true |    NOT FOUND (Empty) |                                          |           | 
 0:36:10                               @DiscordEmbedBuilder | @DiscordEmbedBuilder |      false |      false |             GAME DIR | da39a3ee5e6b4b0d3255bfef95601890afd80709 |  11fdd19c | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\@DiscordEmbedBuilder
 0:36:10                                            @extDB3 |              @extDB3 |      false |      false |             GAME DIR | da39a3ee5e6b4b0d3255bfef95601890afd80709 |  11fdd19c | C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\@extDB3
 0:36:10 ==========================================================================================================================================================================================================
 0:36:10 InitSound ...
 0:36:10 InitSound - complete
 0:36:10 PhysX3 SDK Init started ...
 0:36:10 PhysX3 SDK Init ended.
 0:36:13 Warning Message: You cannot play/edit this mission; it is dependent on downloadable content that has been deleted.\na3_characters_f
 0:36:13 Loading movesType CfgGesturesMale
 0:36:13 Creating action map cache
 0:36:13 MovesType CfgGesturesMale load time 73.0 ms
 0:36:13 Loading movesType CfgMovesMaleSdr
 0:36:13 Reading cached action map data
 0:36:14 Warning: looped for animation: a3\anims_f_epa\data\anim\sdr\cts\hubcleaned\briefing\hubbriefing_loop.rtm differs (looped now 0)! MoveName: hubbriefing_ext
 0:36:14 Warning: looped for animation: a3\anims_f_epa\data\anim\sdr\cts\hubcleaned\spectator\hubspectator_stand.rtm differs (looped now 1)! MoveName: hubspectator_stand_contact
 0:36:14 MovesType CfgMovesMaleSdr load time 1204.0 ms
 0:36:14 Initializing Steam server - Game Port: 2302, Steam Query Port: 2303
 0:36:14 Steam AppId in current environment: 107410
 0:36:14 Unsupported language English in stringtable
 0:36:14  ➥ Context: mpmissions\EdenRP.Altis\stringtable.xml
 0:36:14 Unsupported language English in stringtable
 0:36:14  ➥ Context: mpmissions\EdenRP.Altis\stringtable.xml
 0:36:24 Mission EdenRP.Altis: Number of roles (178) is different from 'description.ext::Header::maxPlayer' (100)
 0:36:24 Connected to Steam servers
 0:36:25 Starting mission:
 0:36:25  Mission file: EdenRP
 0:36:25  Mission world: Altis
 0:36:25  Mission directory: mpmissions\EdenRP.Altis\
 0:36:28 Strange convex component202 in a3\structures_f\households\house_small01\d_house_small_01_v1_f.p3d:geometryView
 0:36:28 Strange convex component203 in a3\structures_f\households\house_small01\d_house_small_01_v1_f.p3d:geometryView
 0:36:28 Strange convex component145 in a3\plants_f\tree\t_pinuss2s_b_f.p3d:geometryView
 0:36:28 Strange convex component149 in a3\plants_f\tree\t_pinuss2s_b_f.p3d:geometryView
 0:36:28 Strange convex component114 in a3\structures_f\households\house_big01\u_house_big_01_v1_f.p3d:geometryView
 0:36:32 Attempt to override final function - eden_fnc_altismapdata
 0:36:32 Attempt to override final function - eden_fnc_altismapdata_meta
 0:36:33 "EdenRP_S1/BIS_fnc_log: [BIS_fnc_preload] ----- Initializing scripts in EdenRP -----"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [recompile] recompile BIS_fnc_missionTasksLocal"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [recompile] recompile BIS_fnc_missionConversationsLocal"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [recompile] recompile BIS_fnc_missionFlow"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] BIS_fnc_feedbackMain (0 ms)"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] BIS_fnc_missionHandlers (0 ms)"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] BIS_fnc_storeParamsValues (0 ms)"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] ""DeltaTime computation started"""
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] BIS_fnc_keyframeAnimation_deltaTime (0 ms)"
 0:36:33 CallExtension loaded: DiscordEmbedBuilder (C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\@DiscordEmbedBuilder\DiscordEmbedBuilder_x64.dll) [1.0.0]

 0:36:33 DiscordEmbedBuilder loaded successfully
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] DiscordEmbedBuilder_fnc_init (41.0004 ms)"
 0:36:33 "---------------------------------------------------------------------"
 0:36:33 "---------------------------------------------------------------------"
 0:36:33 CallExtension loaded: extDB3 (C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\@extDB3\extDB3_x64.dll) [extDB3 v1033 Windows]

 0:36:33 "extDB3 Loaded"
 0:36:33 "---------------------------------------------------------------------"
 0:36:33 "---------------------------------------------------------------------"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] extDB3_fnc_preInit (20.9999 ms)"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] BIS_fnc_getServerVariable (0 ms)"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] BIS_fnc_OM_HandleMapControls (0 ms)"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [BIS_fnc_OM_moduleSmartMarkers] Error: SmartMarker system has not been yet initialized."
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] BIS_fnc_OM_moduleSmartMarkers (0 ms)"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] EDEN_fnc_fuckShit (0 ms)"
 0:36:33 "EdenRP_S1/BIS_fnc_log: [preInit] EDEN_fnc_initTimedMarkers (0 ms)"
 0:36:33 Strange convex component322 in a3\structures_f\research\dome_big_f.p3d:geometryFire
 0:36:33 Strange convex component327 in a3\structures_f\research\dome_big_f.p3d:geometryFire
 0:36:34 "init first"
 0:36:34 "EdenRP: String functions initialized successfully"
 0:36:34 "EdenRP_S1/BIS_fnc_log: [script] initServer.sqf"
 0:36:34 "EdenRP_S1/BIS_fnc_log: [postInit] BIS_fnc_missionFlow (0 ms)"
 0:36:34 "EdenRP_S1/BIS_fnc_log: [postInit] BIS_fnc_initParams (0 ms)"
 0:36:34 "EdenRP_S1/BIS_fnc_log: [postInit] BIS_fnc_initRespawn (0 ms)"
 0:36:34 "EdenRP_S1/BIS_fnc_log: [postInit] BIS_fnc_keyframeAnimation_init (3.99971 ms)"
 0:36:34 "EdenRP_S1/BIS_fnc_log: [postInit] BIS_fnc_reviveInit (0 ms)"
 0:36:34 d:\Bis\Source\Stable\Futura\lib\Network\networkServer.cpp NetworkServer::OnClientStateChanged:NOT IMPLEMENTED - briefing!
 0:36:34  Mission id: 575e62aad15930b0b70204d7ba94582772494531
 0:36:34 Attempt to override final function - bis_fnc_storeparamsvalues_data
 0:36:34 Strange convex component16 in a3\boat_f_jets\carrier_01\carrier_01_hull_05_2_f.p3d:geometry
 0:36:34 Strange convex component17 in a3\boat_f_jets\carrier_01\carrier_01_hull_05_2_f.p3d:geometry
 0:36:34 Strange convex component197 in a3\boat_f_jets\carrier_01\carrier_01_hull_06_2_f.p3d:geometryFire
 0:36:34 Strange convex component199 in a3\boat_f_jets\carrier_01\carrier_01_hull_06_2_f.p3d:geometryFire
 0:36:34 Strange convex component41 in a3\boat_f_jets\carrier_01\carrier_01_hull_09_1_f.p3d:geometryView
 0:36:34 Strange convex component42 in a3\boat_f_jets\carrier_01\carrier_01_hull_09_1_f.p3d:geometryView
 0:36:34 Strange convex component43 in a3\boat_f_jets\carrier_01\carrier_01_hull_09_1_f.p3d:geometryView
 0:36:34 Strange convex component44 in a3\boat_f_jets\carrier_01\carrier_01_hull_09_1_f.p3d:geometryView
 0:36:34 Strange convex component45 in a3\boat_f_jets\carrier_01\carrier_01_hull_09_1_f.p3d:geometryView
 0:36:34 Strange convex component46 in a3\boat_f_jets\carrier_01\carrier_01_hull_09_1_f.p3d:geometryView
 0:36:34 Strange convex component61 in a3\structures_f_enoch\military\barracks\barracks_06_f.p3d:geometryView
 0:36:34 "EdenRP: Setting initial client variables..."
 0:36:34 "EdenRP: Initial client variables published"
 0:36:34 "EdenRP: Creating hospital spawn objects early..."
 0:36:34 "EdenRP: Hospital spawn objects created early - SUCCESS"
 0:36:34 "EdenRP: Loading server functions..."
 0:36:40 "EdenRP_S1/BIS_fnc_log: [BIS_fnc_preload] ----- Scripts initialized at 6984 ms -----"
 0:36:40 "EdenRP: Initializing server functions manually..."
 0:36:41 "EdenRP: Systems functions loaded successfully"
 0:36:41 "EdenRP: MySQL functions loaded successfully"
 0:36:41 "EdenRP: POS functions loaded successfully"
 0:36:42 "EdenRP: Wanted System functions loaded successfully"
 0:36:42 "EdenRP: Housing functions loaded successfully"
 0:36:42 "EdenRP: Gang functions loaded successfully"
 0:36:43 "EdenRP: Events functions loaded successfully"
 0:36:43 "EdenRP: Federal functions loaded successfully"
 0:36:43 "EdenRP: All server functions loaded successfully!"
 0:36:43 "EdenRP: Server function initialization complete."
 0:36:43 "EdenRP: Verifying server functions..."
 0:36:43 "EdenRP: EDENS_fnc_diagLog is available!"
 0:36:43 "EdenRP: EDENS_fnc_asyncCall is available!"
 0:36:43 "EdenRP: EDENS_fnc_initHC is available!"
 0:36:43 "EdenRP: Server function verification completed - continuing initialization..."
 0:36:43 "This is EdenRP server 1 (Profile: EdenRP_S1)"
 0:36:43 "EdenRP_S1: This is EdenRP server 1 (Profile: EdenRP_S1)"
 0:36:43 "EdenRP: Initializing Headless Client support..."
 0:36:43 "EdenRP: Headless Client initialization started."
 0:36:43 "EdenRP: Starting database connection process..."
 0:36:43 "EdenRP: Creating new SQL ID..."
 0:36:43 "EdenRP: Generated SQL ID: {4998}"
 0:36:43 "EdenRP: Attempting extDB3 connection..."
 0:36:43 "EdenRP: Step 1 - Getting extDB3 version..."
 0:36:43 "EdenRP: extDB3 Version result: '1.033'"
 0:36:43 "EdenRP_S1: extDB3: Version: 1.033"
 0:36:43 "EdenRP: Step 2 - Adding database connection..."
 0:36:43 "EdenRP: ADD_DATABASE result: '[1]'"
 0:36:43 "EdenRP: Step 3 - Adding database protocol..."
 0:36:43 "EdenRP: ADD_DATABASE_PROTOCOL result: '[1]'"
 0:36:43 "EdenRP: Database connection successful!"
 0:36:43 "EdenRP_S1: extDB3: Database connection successful!"
 0:36:43 "EdenRP: Attempting to lock extDB3..."
 0:36:43 "EdenRP: extDB3 locked successfully"
 0:36:43 "EdenRP_S1: extDB3: Database locked successfully"
 0:36:43 "EdenRP: Database connection process completed."
 0:36:43 "EdenRP: Testing database connection..."
 0:36:43 "EdenRP: Database test query result: [1]"
 0:36:43 "EdenRP: Database connection test SUCCESSFUL!"
 0:36:43 "EdenRP: Market check result: [1]"
 0:36:43 "EdenRP: Market data already exists in database"
 0:36:43 "EdenRP: Existing market query result: [""[0,32,48,94,94,290,320,928,620,0,1229,1725,1164,1349,1280,1313,1441,2268,1630,0,1976,1837,2581,2115,2255,7980,8406,8820,42500]""]"
 0:36:43 Error in expression <MarketData = (_existingMarket select 0) select 0;
diag_log format["EdenRP: Raw m>
 0:36:43   Error position: <select 0;
diag_log format["EdenRP: Raw m>
 0:36:43   Error Generic error in expression
 0:36:43 File mpmissions\EdenRP.Altis\eden_server\init.sqf..., line 321
 0:36:49 "EdenRP: Setting vehicles loaded after 10 second timeout for client compatibility"
 0:36:49 "EdenRP: Setting gang vehicles loaded after 10 second timeout for client compatibility"
 0:36:49 "EdenRP: Setting session completed after 10 second timeout for client compatibility"
 0:37:05 BEServer: registering a new player #**********
 0:38:35 No speaker given for 'Jack Turner'
 0:38:35 Speaker Male01_F not found in CfgVoiceTypes
 0:38:37 "EdenRP: Player data request - UID: *****************, Side: WEST"
 0:38:38 "EdenRP: Executing player data query: SELECT playerid, name, cash, bankacc, adminlevel, designer_level, developer_level, civcouncil_level, restrictions_level, newdonor, cop_licenses, coplevel, cop_gear, aliases, player_stats, wanted, blacklist, supportteam, vigiarrests, vigiarrests_stored, deposit_box FROM players WHERE playerid='*****************'"
 0:38:38 "EdenRP: Player data query result: [""*****************"",""zzz"",999999,2.5e+07,4,0,4,0,0,0,""[[`license_cop_air`,1],[`license_cop_cg`,1]]"",7,""[`U_I_CombatUniform`,`V_PlateCarrier2_blk`,`B_Bergen_dgtl_F`,`G_Balaclava_TI_blk_F`,`H_PilotHelmetFighter_B`,[`ItemMap`,`ItemCompass`,`ItemWatch`,`ItemRadio`,`ItemGPS`],`arifle_MX_SW_Black_F`,``,``,[`FirstAidKit`,`FirstAidKit`,`FirstAidKit`,`FirstAidKit`,`FirstAidKit`],[`ToolKit`,`ToolKit`],[`FirstAidKit`,`FirstAidKit`,`FirstAidKit`,`FirstAidKit`],[[`HandGrenade_Stone`,1,2],[`100Rnd_65x39_caseless_black_mag_tracer`,100,6],[`100Rnd_65x39_caseless_black_mag_tracer`,95,1]],[`muzzle_snds_65_TI_blk_F`,`acc_flashlight`,`optic_Arco_AK_blk_F`,`bipod_01_F_blk`],[``,``,``,``],[[[`water`,3],[`coffee`,7],[`fuelF`,1],[`tbacon`,3],[`spikeStrip`,1],[`defusekit`,1],[`gpstracker`,1],[`egpstracker`,1],[`heliTowHook`,1],[`bloodbag`,2],[`epiPen`,1],[`dopeShot`,1],[`blindfold`,2],[`vehAmmo`,1],[`baitcar`,1],[`painkillers`,2],[`gokart`,1]],100]]"",""[`zzz`]"",""[0,0,0,90,6,0,0,0,5,0]""
 0:38:38 "EdenRP_S1: ------------- Client Query Request -------------"
 0:38:38 "EdenRP_S1: QUERY: SELECT playerid, name, cash, bankacc, adminlevel, designer_level, developer_level, civcouncil_level, restrictions_level, newdonor, cop_licenses, coplevel, cop_gear, aliases, player_stats, wanted, blacklist, supportteam, vigiarrests, vigiarrests_stored, deposit_box FROM players WHERE playerid='*****************'"
 0:38:38 "EdenRP_S1: Time to complete: 0.0449982 (in seconds)"
 0:38:38 "EdenRP_S1: Player Query Result: [""*****************"",""zzz"",""999999"",""********"",4,0,4,0,0,""0"",[[""license_cop_air"",true],[""license_cop_cg"",true]],7,[""U_I_CombatUniform"",""V_PlateCarrier2_blk"",""B_Bergen_dgtl_F"",""G_Balaclava_TI_blk_F"",""H_PilotHelmetFighter_B"",[""ItemMap"",""ItemCompass"",""ItemWatch"",""ItemRadio"",""ItemGPS""],""arifle_MX_SW_Black_F"","""","""",[""FirstAidKit"",""FirstAidKit"",""FirstAidKit"",""FirstAidKit"",""FirstAidKit""],[""ToolKit"",""ToolKit""],[""FirstAidKit"",""FirstAidKit"",""FirstAidKit"",""FirstAidKit""],[[""HandGrenade_Stone"",1,2],[""100Rnd_65x39_caseless_black_mag_tracer"",100,6],[""100Rnd_65x39_caseless_black_mag_tracer"",95,1]],[""muzzle_snds_65_TI_blk_F"",""acc_flashlight"",""optic_Arco_AK_blk_F"",""bipod_01_F_blk""],["""","""","""",""""],[[[""water"",3],[""coffee"",7],[""fuelF"",1],[""tbacon"",3],[""spikeStrip"",1],[""defusekit"",1],[""gpstracker"",1],[""egpstracker"",1],[""heliTowHook"",1],[""bloodbag"",2],[""epiPen"",1],[""dopeShot"",1],[""blindfold"
 0:38:38 "EdenRP_S1: ------------------------------------------------"
 0:38:38 "EdenRP: Sending player data response to client 4"
 0:38:38 Error in expression <"title"))) && ((getPlayerUID player) in oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error position: <oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error Undefined variable in expression: oev_title_pid
 0:38:38 File mpmissions\EdenRP.Altis\eden_server\Functions\MySQL\fn_queryRequest.sqf..., line 297
 0:38:38 Error in expression <"title"))) && ((getPlayerUID player) in oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error position: <oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error Undefined variable in expression: oev_title_pid
 0:38:38 File mpmissions\EdenRP.Altis\eden_server\Functions\MySQL\fn_queryRequest.sqf..., line 297
 0:38:38 Error in expression <"title"))) && ((getPlayerUID player) in oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error position: <oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error Undefined variable in expression: oev_title_pid
 0:38:38 File mpmissions\EdenRP.Altis\eden_server\Functions\MySQL\fn_queryRequest.sqf..., line 297
 0:38:38 Error in expression <"title"))) && ((getPlayerUID player) in oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error position: <oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error Undefined variable in expression: oev_title_pid
 0:38:38 File mpmissions\EdenRP.Altis\eden_server\Functions\MySQL\fn_queryRequest.sqf..., line 297
 0:38:38 Error in expression <"title"))) && ((getPlayerUID player) in oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error position: <oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error Undefined variable in expression: oev_title_pid
 0:38:38 File mpmissions\EdenRP.Altis\eden_server\Functions\MySQL\fn_queryRequest.sqf..., line 297
 0:38:38 Error in expression <"title"))) && ((getPlayerUID player) in oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error position: <oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error Undefined variable in expression: oev_title_pid
 0:38:38 File mpmissions\EdenRP.Altis\eden_server\Functions\MySQL\fn_queryRequest.sqf..., line 297
 0:38:38 Error in expression <"title"))) && ((getPlayerUID player) in oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error position: <oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error Undefined variable in expression: oev_title_pid
 0:38:38 File mpmissions\EdenRP.Altis\eden_server\Functions\MySQL\fn_queryRequest.sqf..., line 297
 0:38:38 Error in expression <"title"))) && ((getPlayerUID player) in oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error position: <oev_title_pid)) then {
["oev_currentTitl>
 0:38:38   Error Undefined variable in expression: oev_title_pid
 0:38:38 File mpmissions\EdenRP.Altis\eden_server\Functions\MySQL\fn_queryRequest.sqf..., line 297
 0:38:42 Error in expression < {
_return = _forEachIndex;
};
}foreach _stack;

_return;>
 0:38:42   Error position: <_stack;

_return;>
 0:38:42   Error Undefined variable in expression: _stack
 0:38:42 File mpmissions\EdenRP.Altis\core\functions\fn_index.sqf..., line 12
 0:38:42 Error in expression <etID,false];
};

private _index = [_uid,eden_wanted_list] call EDEN_fnc_index;
i>
 0:38:42   Error position: <eden_wanted_list] call EDEN_fnc_index;
i>
 0:38:42   Error Undefined variable in expression: eden_wanted_list
 0:38:42 File mpmissions\EdenRP.Altis\eden_server\Functions\WantedSystem\fn_wantedPardon.sqf..., line 13
 0:38:44 Error in expression <_gang radioChannelAdd [_unit];
} else {
eden_radio_gang radioChannelRemove [_uni>
 0:38:44   Error position: <eden_radio_gang radioChannelRemove [_uni>
 0:38:44   Error Undefined variable in expression: eden_radio_gang
 0:38:44 File mpmissions\EdenRP.Altis\eden_server\Functions\Systems\fn_managesc.sqf..., line 18
 0:38:44 Error in expression <!= 5) then {
if (_adminLevel > 0) then {eden_radio_admin radioChannelAdd [_unit]>
 0:38:44   Error position: <eden_radio_admin radioChannelAdd [_unit]>
 0:38:44   Error Undefined variable in expression: eden_radio_admin
 0:38:44 File mpmissions\EdenRP.Altis\eden_server\Functions\Systems\fn_managesc.sqf..., line 22
 0:38:44 Error in expression <ide) do {
case west: {
if(_bool) then {
eden_radio_west radioChannelAdd [_unit];>
 0:38:44   Error position: <eden_radio_west radioChannelAdd [_unit];>
 0:38:44   Error Undefined variable in expression: eden_radio_west
 0:38:44 File mpmissions\EdenRP.Altis\eden_server\Functions\Systems\fn_managesc.sqf..., line 26
 0:41:48 Error in expression < then {
_list pushBack _x;
};
} forEach eden_wanted_list;

[_list] remoteExec [">
 0:41:48   Error position: <eden_wanted_list;

[_list] remoteExec [">
 0:41:48   Error Undefined variable in expression: eden_wanted_list
 0:41:48 File mpmissions\EdenRP.Altis\eden_server\Functions\WantedSystem\fn_wantedFetch.sqf..., line 15
