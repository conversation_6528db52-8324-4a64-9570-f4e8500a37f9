//  File: fn_jailMe.sqf
//	Author <PERSON> "<PERSON>" Boardwine

//	Description: Once word is received by the server the rest of the jail execution is completed.
private["_bail","_esc","_countDown","_bounty","_lastSync","_trashObjs","_perkTier","_jailTimeChange"];
params [
	["_ret",[],[[]]],
	["_bad",false,[false]]
];

_lastSync = time + 150;
_bounty = (eden_is_arrested select 2);
eden_jailTime = time + (eden_is_arrested select 1);
life_bail_amount = _bounty;
_jailTimeChange = 1;
if(count _ret > 0) then {
	_bounty = ((eden_is_arrested select 2) + (_ret select 3));
	life_bail_amount = _bounty;
	_perkTier = ["civ_jailTime"] call EDEN_fnc_fetchStats;
	_jailTimeChange = switch (_perkTier) do {
		case 1: {0.95};
		case 2: {0.90};
		case 3: {0.85};
		default {1};
	};
	eden_jailTime = time + ((_bounty * 0.0024) * _jailTimeChange);
	if((eden_jailTime > (time + 4800)) || (_bounty > 2000000)) then {
		eden_jailTime = time + 4800;
		life_bail_amount = 2000000;
		_bounty = 2000000;
	};
};
if((eden_jailTime < (time + 290)) || _bounty < 62500) then {eden_jailTime = time + 300; _bounty = 62500; life_bail_amount = 62500;};
eden_is_arrested set [0,1];
eden_is_arrested set [1,round(eden_jailTime - time)];
eden_is_arrested set [2,_bounty];
[5] call EDEN_fnc_ClupdatePartial;

if (count _ret > 0) then {
	if (((_ret select 3) > 2500) && (((_ret select 3) * 0.2) < (eden_atmcash - 100000))) then {
		_perkTier = ["civ_copKills"] call EDEN_fnc_fetchStats;
		private _jailFeeChange = switch (_perkTier) do {
			case 1: {0.95};
			case 2: {0.90};
			case 3: {0.85};
			case 4: {0.80};
			case 5: {0.75};
			default {1};
		};
		eden_atmcash = eden_atmcash - (((_ret select 3) * 0.2) * _jailFeeChange);
		eden_cache_atmcash = eden_cache_atmcash - (((_ret select 3) * 0.2) * _jailFeeChange);
		systemChat format["You were sentenced to pay $%1 and serve %2 minutes in jail.",[((_bounty * 0.2) * _jailFeeChange)] call EDEN_fnc_numberText,(round(eden_jailTime - time)/60)];
	};
};
["prison_time",(round(eden_jailTime - time)/60)] spawn EDEN_fnc_statArrUp;
if(EDEN_stats_crimes select 0 > 0) then {
	[[getPlayerUID player],"EDENS_fnc_wantedPardon",false,false] spawn EDEN_fnc_MP;
};

//Give them a jail uniform
private _jailUniform = uniform player;
private _jailBackpack = backpack player;
player allowDamage false;
removeUniform player;
removeBackpack player;
player forceAddUniform "U_C_WorkerCoveralls";
player setVariable ["jailed",true,true];
private _dam_obj = player;
_dam_obj setDamage 0;
eden_thirst = 100;
eden_hunger = 100;
eden_ses_last_pos = [];
[] call EDEN_fnc_hudUpdate;
_esc = false;
_bail = false;

eden_jail_escBuffer = time + 600;
eden_canpay_bail = false;
systemChat "You can pay bail after 10 minutes of time served.";

if((round(eden_jailTime - time)) >= 900) then {
	private _trashLocations = [
		[16676.1,13610.3,1.80224],
		[16690.5,13595,0.965832],
		[16715.9,13627.1,1.65066],
		[16688.7,13636.2,2.44119]
	];

	_trashObjs = [];
	{
		_obj_main = "Land_GarbageBags_F" createVehicleLocal _x;
		_obj_main setPos _x;
		_obj_main addAction ["Search Trash",EDEN_fnc_trashJail,'',1.5,false,false,'','!eden_action_inUse && !(_target getVariable "deactivated")',4];
		_obj_main setVariable ["deactivated",false];
		_trashObjs pushBack _obj_main;
	} forEach _trashLocations;
};

while {true} do {
	if((round(eden_jailTime - time)) > 0) then {
		_countDown = [(eden_jailTime - time),"MM:SS"] call BIS_fnc_secondsToString;
		hintSilent parseText format[(localize "STR_Jail_Time")+ "<br/> <t size='2'><t color='#FF0000'>%1</t></t><br/><br/>" +(localize "STR_Jail_Pay")+ " %3<br/>" +(localize "STR_Jail_Price")+ " $%2<br/><br/>You can search the trash piles for prison contraband to turn in to the prison guard in the tower.<br/><br/>Contraband Found: %4",_countDown,[life_bail_amount] call EDEN_fnc_numberText,if(isNil "eden_canpay_bail") then {"Yes"} else {"No"},profileNamespace getVariable ["contraband",0]];
	};

	if !(eden_holdJailTime) then {
		eden_is_arrested set[1,round(eden_jailTime - time)];
	};
	eden_is_arrested set[2,(round(eden_jailTime - time)/0.0048)];

	if ((round(time) > round(_lastSync)) && !(eden_holdJailTime)) then {
		_lastSync = time + 150;
		[5] call EDEN_fnc_ClupdatePartial;
		life_bail_amount = (round(eden_jailTime - time)/0.0048);
	};

	if(time > eden_jail_escBuffer) then {
		eden_canpay_bail = nil;
	} else {
		eden_canpay_bail = false;
	};

	if(secondaryWeapon player != "" || primaryWeapon player != "" || handgunWeapon player != "") then {
		removeAllWeapons player;
	};

	if(vehicle player != player) exitWith {
		_dam_obj setDamage 1;
	};

	if((jailwall getVariable["safe_open",false]) && (time > eden_jail_escBuffer) && (player distance eden_jailPos2) > 25.8) exitWith {
		_esc = true;
	};

	if !(isForcedWalk player) then {
		player forceWalk true;
	};

	if(((player distance eden_jailPos2) > 27)) then	{
		player setPosAsl eden_jailPos1;

		if(jailwall getVariable["safe_open",false]) then {
			systemChat "You must serve at least 10 minutes before escaping!";
		} else {
			systemChat "Get away from that wall sucka!";
		};
	};

	if(eden_bail_paid) exitWith {
		_bail = true;
	};

	if((round(eden_jailTime - time)) < 1) exitWith {hint ""};
	if(!alive player && ((round(eden_jailTime - time)) > 0)) exitWith {};
	uiSleep 1;
};

player forceWalk false;
if (alive player) then {
	profileNamespace setVariable ["contraband",0];
};
switch (true) do {
	case (_bail) : {
		eden_is_arrested = [0,0,0];
		eden_bail_paid = false;
		hint localize "STR_Jail_Paid";
		serv_wanted_remove = [player];
		player setPos (getMarkerPos "jail_release");
		player allowDamage true;
		[[getPlayerUID player],"EDENS_fnc_wantedRemove",false,false] spawn EDEN_fnc_MP;
		[5] call EDEN_fnc_ClupdatePartial;
		if !(_jailUniform in eden_illegal_gear) then {
			removeUniform player;
			player forceAddUniform _jailUniform;
		};
		player addBackpack _jailBackpack;
		player setVariable ["jailed",false,true];
	};

	case (_esc) : {
		eden_is_arrested = [0,0,0];
		hint localize "STR_Jail_EscapeSelf";
		player allowDamage true;
		[[0,"STR_Jail_EscapeNOTF",true,[profileName]],"EDEN_fnc_broadcast",-2,false] spawn EDEN_fnc_MP;
		[[getPlayerUID player,profileName,"3",player],"EDENS_fnc_wantedAdd",false,false] spawn EDEN_fnc_MP;
		[5] call EDEN_fnc_ClupdatePartial;
		player setVariable ["jailed",false,true];
	};


	case (alive player && !_esc && !_bail) : {
		eden_is_arrested = [0,0,0];
		hint localize "STR_Jail_Released";
		[[getPlayerUID player],"EDENS_fnc_wantedRemove",false,false] spawn EDEN_fnc_MP;
		player allowDamage true;
		player setPos (getMarkerPos "jail_release");
		[5] call EDEN_fnc_ClupdatePartial;
		if !(_jailUniform in eden_illegal_gear) then {
			removeUniform player;
			player forceAddUniform _jailUniform;
		};
		player addBackpack _jailBackpack;
		player setVariable ["jailed",false,true];
	};
};

if((round(eden_jailTime - time)) >= 900) then {
	{
		deleteVehicle _x;
	} forEach _trashObjs;
};
