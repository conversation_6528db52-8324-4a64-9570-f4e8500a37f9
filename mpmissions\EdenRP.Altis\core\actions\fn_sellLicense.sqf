//  File: fn_sellLicense.sqf
//	Author: [OS] Odin
//	Description: Called when selling a license. May need to be revised.
private["_type"];

if(isNil "eden_cash") then {eden_cash = 0; eden_cache_cash = eden_random_cash_val;};
if(isNil "eden_atmcash") then {eden_atmcash = 0; eden_cache_atmcash = eden_random_cash_val;};

_type = _this select 3;

_price = [_type] call EDEN_fnc_licensePrice;
_license = [_type,0] call EDEN_fnc_licenseType;

if((eden_cash + (eden_random_cash_val - 5000)) > eden_cache_cash || (eden_atmcash + (eden_random_cash_val - 5000)) > eden_cache_atmcash) exitWith {
	[["event","Hacked Cash"],["player",name player],["player_id",getPlayerUID player],["hackedcash",eden_cash - (eden_cache_cash - eden_random_cash_val)],["hackedbank",eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)],["location",getPosATL player]] call EDEN_fnc_logIt;
	[[profileName,format["Hacked Cash Detected! (Cash Hacked In = %1) (Bank Hacked In = %2)",eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDEN_fnc_notifyAdmins",-2,false] spawn EDEN_fnc_MP;
	[[1,player,[eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDENS_fnc_handleDisc",false,false] spawn EDEN_fnc_MP;
	["HackedMoney",false,false] call compile PreProcessFileLineNumbers "\a3\functions_f\Misc\fn_endMission.sqf";
};

if(_type == "wpl") then {
	if(license_civ_wpl) then {
		license_civ_wpl = false;
		hint "Your Workers Protection License has been refunded at half price.";
	};
};

eden_atmcash = eden_atmcash + (_price * 0.5);
eden_cache_atmcash = eden_cache_atmcash + (_price * 0.5);
titleText[format[localize "STR_NOTF_S_1", _license select 1,[(_price * 0.5)] call EDEN_fnc_numberText],"PLAIN DOWN"];
