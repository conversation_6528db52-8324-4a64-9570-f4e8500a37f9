//  File: fn_searchAction.sqf
//	Author: <PERSON> "<PERSON>" <PERSON>wine
//	Description: Starts the searching process.
private["_unit"];
_unit = param [0,Obj<PERSON><PERSON>,[Obj<PERSON><PERSON>]];
if(isNull _unit) exitWith {};
hint localize "STR_NOTF_Searching";
uiSleep 2;
if(player distance _unit > 5 || !alive player || !alive _unit) exitWith {hint localize "STR_NOTF_CannotSearchPerson"};
[[player],"EDEN_fnc_searchClient",_unit,false] spawn EDEN_fnc_MP;
eden_action_inUse = true;
