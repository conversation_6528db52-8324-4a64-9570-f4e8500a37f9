//  File: fn_helpHints.sqf
//	Author: <PERSON> "tkc<PERSON><PERSON>" Schultz
//	Description: Informs new players of helpful stuff.

private _array = [
	"<t color='#ffff00' size='2' align='center'>TeamSpeak 3 Server</t><br/><br/><t align='center' size='1.5' color='#00ff99'>ts.EdenRP.com</t>",
	"<t color='#ffff00' size='2' align='center'>Our Website</t><br/><br/><t align='center' size='1.5' color='#00ff99'>EdenRP.com</t>",
	"<t color='#ffff00' size='2' align='center'>Need Support?</t><br/><br/><t align='center'>You can join our TeamSpeak server for quick help by Support Team members or visit our website at EdenRP.com/support. You can also go to the our website and just click on the support button at the top of the page!</t>",
	format ["<t color='#ffff00' size='2' align='center'>Current Server</t><br/><br/><t align='center'>You are currently playing on EdenRP Server #%1. Thanks for playing on EdenRP Servers!</t>",edenrp_server],
	"<t color='#ffff00' size='2' align='center'>Whitelisted Factions</t><br/><br/>Looking to join the APD or RnR? You need at least 1,680 minutes of civilian playtime on our servers and must be at least 16 years of age. For further requirements check out our website at EdenRP.com",
	"<t color='#ffff00' size='2' align='center'>Rules - RP/RDM</t><br/><br/>When engaging role-play, you must give the player time to react (at least 5 seconds) do not immediately fire on the player. If a player abides by your demands, role-play it from there do not just shoot them. Killing another player without any role-play will result in administrative action. RP has a 5 minute window, once engaged you are still engaged for this amount of time. Side Chat does not engage role-play. Also you can not type in direct chat to engage role-play.",
	"<t color='#ffff00' size='2' align='center'>Rules - Combat Log</t><br/><br/>Storing a vehicle while engaged with another player is considered combat logging. Disconnecting or aborting while in an active situation with another player is not allowed. Killing yourself on purpose while engaged with a player is considered combat logging/fail RP. Logging out specifically to avoid an obvious encounter from happening is also considered combat logging.",
	"<t color='#ffff00' size='2' align='center'>New Life Rule</t><br/><br/>If you die for any reason apart from dying by a Arma bug you may not return to that area for 15 minutes (1 Kilometer from where you died).  APD may return to legal areas after death as they see fit (This is to simulate the size of an actual police force). If APD die in an illegal area they are to return in waves.",
	"<t color='#ffff00' size='2' align='center'>Rules - Redzones</t><br/><br/>Red zones are Kill on Sight (KOS), no RP is required by Civilians. Gas Stations are never redzones. APD may patrol red zones excluding Rebel outposts every 15 minutes",
	"<t color='#ffff00' size='2' align='center'>Workers Protection</t><br/><br/>Stop by your local gun store and pick yourself up a Workers Protection License! The license will give you an increased profit on legal item sales and also give you access to better personal protection weapons from the gun store! The license will prevent you from harvesting illegal items and also get replaced if you purchase a Vigilante or Rebel License! It's a great place to start!",
	"<t color='#ffff00' size='2' align='center'>Black Markets</t><br/><br/>Scared of processing illegal drugs in a big empty field? Capture a black market with your gang to process specific drugs with the protection of a convenient hide out. Do know however, this will come with a tax for less quality drugs.",
	"<t color='#ffff00' size='2' align='center'>Vigilante</t><br/><br/>Finding lots of criminals on the streets? After two hours of time in game, become a vigilante and take to the streets! As a vigilante, you can send wanted players to jail!",
	format ["<t color='#ffff00' size='2' align='center'>EdenRP Server IPs</t><br/><br/>Server 1: *************<br/>Server 2: *************<br/>Server 3: *************<br/>You are currently on Server: %1",edenrp_server],
	"<t color='#ffff00' size='2' align='center'>Helpful Hints</t><br/><br/>The automated helpful hints like this one can be disabled in your settings menu once you have over 10 hours on the server!",
	"<t color='#ffff00' size='2' align='center'>Reporting Players</t><br/><br/>You can report players for infractions on our website (EdenRP.com) by clicking on the support button at the top of the page. Almost all reports will require a form of proof (5 minute videos or VALID screenshots). For more information on acceptable forms of proof visit the website.",
	"<t color='#ffff00' size='2' align='center'>Feedback Tracker</t><br/><br/>You find a bug/exploit? You can report bugs/exploits at (EdenRP.com) by clicking on the support button at the top of the page. You will need to create a new account if you dont have one.",
	"<t color='#ffff00' size='2' align='center'>Server Message</t><br/><br/>Did you know that we have 3 life servers?<br/> You can jump between any of them at anytime! The only thing that will not carry between servers is housing and gang sheds! Your gear, vehicles, money, gang, and more will sync between all 3 servers!",
	"<t color='#ffff00' size='2' align='center'>Marking Vehicles</t><br/><br/>The server automatically cleans up abandoned and/or unused vehicles, so make sure you mark in your keychain menu any vehicles you would like to keep, or simply bind Custom Action 9 in your controls for quicker and easier vehicle persistence marking!"
];

private _stopRepeat = false;
while {true} do {
	if (!(life_newPlayerHints) && (EDEN_stats_playtime_civ > 600)) exitWith {};
	uiSleep 30;
	_stopRepeat = false;
	if (!(_stopRepeat) && (player getVariable["statBounty",250500]) > 0 && (player getVariable["statBounty",250500]) < 250000) then {
		hint parseText "<t color='#ffff00' size='2' align='center'>Courthouse</t><br/><br/>You can pay off your bounty at a local courthouse if you are wanted for less than $250,000 and have no restricted charges!";
		_stopRepeat = true;
	} else {
		hint parseText (selectRandom _array);
	};
	uiSleep 600;
};