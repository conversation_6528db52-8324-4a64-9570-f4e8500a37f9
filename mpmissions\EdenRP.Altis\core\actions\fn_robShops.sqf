#include "..\..\macro.h"
if(scriptAvailable(10)) exitWith {hint "You cannot spam robberies. Nice try though...";};
//  File: fn_robShops.sqf
//	Author: MrKrak<PERSON>
//	Made from MrKrakens bare-bones shop robbing tutorial on www.altisliferpg.com forums
//	Description: Executes the rob shob action!
//	Idea developed by PEpwnzya v2.0

private["_robber","_shop","_payout","_ui","_progress","_pgText","_cP","_rip","_shopPos","_markerName"];
_shop = param [0,ObjNull,[ObjNull]]; //The object that has the action attached to
_robber = param [1,Obj<PERSON>ull,[Obj<PERSON>ull]]; //Player robbing store
_action = param [2]; // addAction index

// Initialize variables
_rip = _shop getVariable "status";
if (isNil "_rip") then {
	_rip = false;
	_shop setVariable["status", _rip, true];
};
_shopPos = getPos _shop;
_markerName = format["storeRobery_%1",_shopPos];
_serverCheckDelay = 300; // 50 seconds more than the time it takes to rob a store

// Check if the shop has been robbed recently
_lastRobbed = _shop getVariable ["lastRobbed",(serverTime - 900)];
if ((serverTime - _lastRobbed) < 900) exitWith {
	hint "This store was robbed recently and has no money left.";
};

//  Check if robbery can be done
if(vehicle player != _robber) exitWith { hint "You need to exit your vehicle!"; };
if (_robber getVariable ["restrained",true]) exitWith {};
if !(alive _robber) exitWith { hint "Can't rob a gas station while dead."; };
if (currentWeapon _robber == "" || currentWeapon _robber in eden_fake_weapons) exitWith { hint "You need a gun to intimidate me!"; };
if (playerSide isEqualTo west) exitWith { hint "You cannot rob a gas station as a cop!"; };
if(_rip) exitWith {
	hint "A robbery is already taking place!";
};

hint "Intimidating clerk...";
//prevent people from robbing at same time.
uiSleep round(random 4);

// Set store state as being robbed
_rip = true;
_shop setVariable["status",_rip,true];
_shop setVariable["lastRobbed",serverTime,true];

// Start server side function to cleanup in the event a player disconnected.
[[_shop,_markerName,_serverCheckDelay],"EDEN_fnc_robShopReset",false,false] spawn EDEN_fnc_MP;


// getting cops online
_copsCount = {side _x isEqualTo west} count playableUnits;

_payout = if (_copsCount > 5) then [{85000}, {35000 + (_copsCount * 10000)}];

// Create a unique marker to indicate this shop is being robbed
_marker = createMarker [_markerName, _shopPos];
_markerName setMarkerColor "ColorOrange";
_markerName setMarkerText "!DANGER! Robbery in progess !DANGER!";
_markerName setMarkerType "mil_warning";

// Time to notify the cops about the robbery. In the dispatch, the name of the nearest city is included
_nearestCity = nearestLocation [ _shopPos, "nameCity"];
[[1,format["%1 is robbing a gas station near the city of %2!", name player,text _nearestCity]],"EDEN_fnc_broadcast",-2,false] spawn EDEN_fnc_MP;
hint parseText "Keep in mind that a gas station robbery is NOT a KOS area!<br/>You must till engage!";
//Setup our progress bar.
disableSerialization;
5 cutRsc ["life_progress","PLAIN DOWN"];
_ui = uiNameSpace getVariable "life_progress";
_progress = _ui displayCtrl 38201;
_pgText = _ui displayCtrl 38202;
_pgText ctrlSetText format["Robbery in Progress, stay close (10m) (1%1)...","%"];
_progress progressSetPosition 0.01;
_cP = 0.01;
private _precash = eden_cash;
private _prebank = eden_atmcash;

if(_rip) then {
	while{true} do {
		//_shop removeAction _action;
		uiSleep  2.50;
		_cP = _cP + 0.01;
		_progress progressSetPosition _cP;
		_pgText ctrlSetText format["Robbery in Progress, stay close (10m) (%1%2)...",round(_cP * 100),"%"];
		if(_cP >= 1) exitWith {};
		if(_robber distance _shop > 10) exitWith {};
		if!(alive _robber) exitWith {};
		if (_robber getVariable ["restrained",true]) exitWith {5 cutText ["","PLAIN DOWN"];};
		playSound3D ["A3\Sounds_F\sfx\alarm_independent.wss", player];
	};

	deleteMarker _markerName;
	if!(alive _robber) exitWith {};
	if (_robber getVariable ["restrained",true]) exitWith {};
	if(_robber distance _shop > 10) exitWith { hint "You need to stay within 10m to rob the cash box! - Now the cash box is locked."; 5 cutText ["","PLAIN DOWN"]; _rip = false; };
	5 cutText ["","PLAIN DOWN"];
	titleText[format["You have stolen $%1, now get away before the cops arrive!",[_payout] call EDEN_fnc_numberText],"PLAIN DOWN"];
	eden_cash = eden_cash + _payout;
	eden_cache_cash = eden_cache_cash + _payout;

	[
		["event","Robbed Gas Station"],
		["player",name player],
		["player_id",getPlayerUID player],
		["value",[_payout] call EDEN_fnc_numberText],
		["location",getPosATL player]
	] call EDEN_fnc_logIt;

	[[getPlayerUID player,profileName,"60",player],"EDENS_fnc_wantedAdd",false,false] spawn EDEN_fnc_MP;
};

eden_use_atm = false;
uiSleep 180;
hint "You may now use ATMs again.";
eden_use_atm = true;
