#include "..\..\macro.h"
//  File: fn_storeVigilanteArrests.sqf
//	Author: Horizon

//	Description: Store and claim vigilante arrests.

private ["_mode","_player","_action","_arrests","_pricePerTier","_storePrice"];

_mode = _this select 3;
_player = player;

if (scriptAvailable(5)) exitWith {hint "Please wait before trying to do this!";};
if (oev_action_inUse) exitWith {titleText ["You are already performing another action!", "PLAIN DOWN"]};
if (_mode isEqualTo 0 && oev_vigiarrests isEqualTo 0) exitWith {hint "You don't have any arrests to store!"};
if (_mode isEqualTo 0 && _player getVariable["statBounty",0] > 0) exitWith {hint "Nice try! You can not store your arrests as a wanted criminal.";};

_action = false;
_pricePerTier = 400000;
_storePrice = switch (true) do {
  case (oev_vigiarrests >= 200): {_pricePerTier * 5};
  case (oev_vigiarrests >= 100): {_pricePerTier * 4};
  case (oev_vigiarrests >= 50): {_pricePerTier * 3};
  case (oev_vigiarrests >= 25): {_pricePerTier * 2};
  default {_pricePerTier};
};

switch (_mode) do {
  //Store arrests
  case 0: {
    _arrests = _player getVariable ["vigilanteArrests",0];
    _action = [
      format ["Are you sure you want to store your %1 vigilante arrests for $%2?",oev_vigiarrests,[_storePrice] call OEC_fnc_numberText],
    	"Confirmation",
    	"Yes",
    	"No"
  	] call BIS_fnc_guiMessage;
    if (_action) then {
      uiSleep (random(3));
      if (oev_atmcash < _storePrice) exitWith {hint "You do not have enough money to store your arrests!"};
      uiSleep (random(3));
      [4, player] remoteExec ["OES_fnc_vigiGetSetArrests",2];
      oev_atmcash = oev_atmcash - _storePrice;
      oev_cache_atmcash = oev_cache_atmcash - _storePrice;
      [1] call OEC_fnc_ClupdatePartial;
    	titleText [format["You stored %1 arrests away for $%2.",_arrests,[_storePrice] call OEC_fnc_numberText],"PLAIN DOWN"];
    	[2] call OEC_fnc_ClupdatePartial;
    };
  };
  //claim arrests
  case 1: {
    _storedArrests = _player getVariable ["vigilanteArrestsStored",0];
    _action = [
      format ["Are you sure you want to claim your arrests? You currently have %1 arrests stored.",oev_vigiarrests_stored],
      "Confirmation",
      "Yes",
      "No"
    ] call BIS_fnc_guiMessage;
    if (_action) then {
      uiSleep (random(3));
      titleText [format["You claimed %1 arrests to your active license.",_storedArrests],"PLAIN DOWN"];
      [5, player] remoteExec ["OES_fnc_vigiGetSetArrests",2];
      [2] call OEC_fnc_ClupdatePartial;
    };
  };
};
