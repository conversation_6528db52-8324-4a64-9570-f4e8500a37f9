//  File: fn_civRestrain.sqf

params [
	["_targetPlayer",objNull,[obj<PERSON><PERSON>]]
];

if (player distance _targetPlayer > 4) exitWith {hint "You are too far away."};

if ((currentWeapon player != "") && !(currentWeapon player in eden_fake_weapons) && ((_targetPlayer getVariable["playerSurrender",false]) || (_targetPlayer getVariable["downed",false]))) then {
	if ([false,"ziptie",1] call EDEN_fnc_handleInv) then {
		[[], "EDEN_fnc_closeMap", _targetPlayer, false] spawn EDEN_fnc_MP;
		uisleep 0.1;
		_targetPlayer setVariable["restrained",true,true];
		_targetPlayer setVariable["zipTied",true,true];
		[[player], "EDEN_fnc_restrain", _targetPlayer, false] spawn EDEN_fnc_MP;
		hint format["%1 restrained.", name _targetPlayer];
		if !(license_civ_vigilante) then {
			if !(side _targetPlayer isEqualTo civilian) then {
				[[getPlayerUID player,name player,"67",player],"EDENS_fnc_wantedAdd",false,false] spawn EDEN_fnc_MP;
			} else {
				[[getPlayerUID player,name player,"9",player],"EDENS_fnc_wantedAdd",false,false] spawn EDEN_fnc_MP;
			};
		} else {
			//For compensation on CLog
			_targetPlayer setVariable["restrainedBy",[player,1],true];
		};
		closeDialog 0;
	} else {
		hint "You have nothing to restrain them with.";
	};
};