if(oev_jailCD > time) exitWith {hint "You cannot spam arrest, please wait 3 seconds.";};
oev_jailCD = time + 3;
//  File: fn_arrestAction.sqf

//	Description: Arrests the targeted person.
params [
	["_unit",obj<PERSON>ull,[obj<PERSON><PERSON>]],
	["_isVigi",false,[false]]
];
if((jailwall getVariable["safe_open",false])) exitWith {hint "The jail is broken you cannot jail prisoners until it is repaired!";};
if(isNull _unit) exitWith {}; //Not valid
if(isNil "_unit") exitwith {}; //Not Valid
if(!(_unit isKindOf "Man")) exitWith {}; //Not a unit
if(!isPlayer _unit) exitWith {}; //Not a human
if(!(_unit getVariable "restrained")) exitWith {}; //He's not restrained.
if(!((side _unit) in [civilian,independent])) exitWith {}; //Not a civ
if(_unit isEqualTo oev_vigiBuddyObj) exitWith {hint "You cannot send your vigi buddy to jail!";};

private _action = [
	format ["Are you sure you want to send %1 to jail?",name _unit],
	"Confirmation",
	"Yes",
	"No"
] call BIS_fnc_guiMessage;

private _storedBounty = 0;


if (_action) then {
	uiSleep floor random 3;
	if((jailwall getVariable["safe_open",false])) exitWith {hint "The jail is broken you cannot jail prisoners until it is repaired!";};
	if(isNil "_unit") exitwith {}; //Not Valid
	if(!(_unit isKindOf "Man")) exitWith {}; //Not a unit
	if(!isPlayer _unit) exitWith {}; //Not a human
	if(!(_unit getVariable "restrained")) exitWith {};
	if(!((side _unit) in [civilian,independent])) exitWith {};
	if(_unit isEqualTo oev_vigiBuddyObj) exitWith {hint "You cannot send your vigi buddy to jail!";};
	if!(alive player) exitWith {};
	if (player distance _unit > 10) exitWith {hint "That player is too far for you to arrest"};

	[[_unit,player,false],"OES_fnc_wantedBounty",false,false] spawn OEC_fnc_MP;
	O_stats_arrestsMade = O_stats_arrestsMade + 1;

	if(isNull _unit) exitWith {};
	detach _unit;
	if (_unit getVariable ["isVigi",false]) then {
		[[2,_unit],"OES_fnc_vigiGetSetArrests",false,false] spawn OEC_fnc_MP; // Wipe their vigi progress
		[
			["event","Lowered a Vigi Tier"],
			["player",name _unit],
			["player_id",getPlayerUID _unit],
			["by",name player],
			["by_id",getPlayerUID player],
			["location",getPosATL player]
		] call OEC_fnc_logIt;

	};
	_storedBounty = (_unit getVariable["statBounty",0]);
	[[_unit,false],"OEC_fnc_jaill",_unit,false] spawn OEC_fnc_MP;
	[[0,"STR_NOTF_Arrested_1",true, [_unit getVariable["realname",name _unit], profileName, [_storedBounty] call OEC_fnc_numberText]],"OEC_fnc_broadcast",-2,false] spawn OEC_fnc_MP;

	if (playerSide isEqualTo civilian) then {
		[[1,player],"OES_fnc_vigiGetSetArrests",false,false] spawn OEC_fnc_MP;
		["vigiarrest",1] call OEC_fnc_statArrUp;
		if (oev_vigiarrests isEqualTo 24) then { // Subtract 1 so it alerts when they make the initial arrest
			hint parseText format ["<t color='#00CC00'><t size='2'>Rank Up: Tier 2</t></t><br/><br/>Congratulations, for arresting 25 people you have advanced a rank! The following perks are now available:<br/><br/><t color='#FF0000'><t size='2'>Weapons:</t></t><br/>ACP .45<br/><br/><t color='#FF0000'><t size='2'>Backpacks:</t></t><br/>Carryall"];
		};
		if (oev_vigiarrests isEqualTo 49) then { // Subtract 1 so it alerts when they make the initial arrest
			hint parseText format ["<t color='#00CC00'><t size='2'>Rank Up: Tier 3</t></t><br/><br/>Congratulations, for arresting 50 people you have advanced a rank! The following perks are now available:<br/><br/><t color='#FF0000'><t size='2'>Weapons:</t></t><br/>Sting 9mm<br/><br/><t color='#FF0000'><t size='2'>Vests:</t></t><br/>Vigilante Vest"];
		};
		if (oev_vigiarrests isEqualTo 99) then { // Subtract 1 so it alerts when they make the initial arrest
			hint parseText format ["<t color='#00CC00'><t size='2'>Rank Up: Tier 4</t></t><br/><br/>Congratulations, for arresting 100 people you have advanced a rank! The following perks are now available:<br/><br/><t color='#FF0000'><t size='2'>Weapons:</t></t><br/>Spar-16 5.56"];
		};
		if (oev_vigiarrests isEqualTo 199) then { // Subtract 1 so it alerts when they make the initial arrest
			hint parseText format ["<t color='#00CC00'><t size='2'>Rank Up: Tier 5</t></t><br/><br/>Congratulations, for arresting 200 people you have advanced a rank! The following perks are now available:<br/><br/><t color='#FF0000'><t size='2'>Weapons:</t></t><br/>Spar-16 5.56"];
		};
	} else {
		["cop_arrests",1] spawn OEC_fnc_statArrUp;
	};

	[_unit] spawn{
		_player = param [0,ObjNull,[ObjNull]];
		if(isNull _player) exitWith {};
		_player hideObject true;
		uiSleep 30;
		if(isNull _player) exitWith {};
		_player hideObject false;
	};
};
