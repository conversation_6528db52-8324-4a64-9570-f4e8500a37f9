// File: fn_vigiNotify.sqf
// Author: <PERSON> "<PERSON>" <PERSON>wine
// Modified: <PERSON> "tkc<PERSON>" Schultz
// From the AsYetUntitled Github --  AL.Altis/core/fn_welcomeNotification.sqf

disableSerialization;
[
        "",
        0,
        0.2,
        10,
        0,
        0,
        8
] spawn BIS_fnc_dynamicText;

createDialog "RscDisplayWelcome";

private _display = findDisplay 999999;
private _text1 = _display displayCtrl 1100;
private _buttonSpoiler = _display displayCtrl 2400;
private _textSpoiler = _display displayCtrl 1101;
private _text2 = _display displayCtrl 1102;
private _title = _display displayCtrl 1000;

private _message = "";
_message = _message + "<t align='center' size='8' shadow='0'></t><br/>";
_message = _message + "<t align='center' size='2' shadow='0'><t color='#ff9900'>Vigilante Rules</t></t><br/>";
_message = _message + "<t align='center'>______________________________________________________________________________________</t><br/><br/>";
_message = _message + "<t align='center' size='1' shadow='0'><t color='#ff9900'>Clicking continue shows that you acknowledge and agree to the following rules below<br/> For the most up to date rules please visit the information section on our forums at</t> <t color='#f6ff00'>EdenRP.com</t><br/><br/>";
_message = _message + "<t align='center'>______________________________________________________________________________________</t><br/><br/>";
_message = _message + "<t align='left'>1. Vigilantes are <t color='#ff0000'>no exception</t> to the law. They must also follow the server rules regarding RDM, VDM etc.<br/></t>";
_message = _message + "<t align='left'>_________________________________________</t><br/><br/>";
_message = _message + "<t align='left'>2. Vigilantes are not required to announce before shooting in red/illegal zones.<br/></t>";
_message = _message + "<t align='left'>      1. The difference between a Vigilante and a Civilian is only that they can place a wanted player in jail.<br/></t>";
_message = _message + "<t align='left'>            1. You are still able to do illegal activities and can be ticketed and/or arrested<br/></t>";
_message = _message + "<t align='left'>_________________________________________</t><br/><br/>";
_message = _message + "<t align='left'>3. Players associated with <t color='#ff0000'>meta-gaming</t> while having a vigilante license may be subjective to receive administrative action.<br/></t>";
_message = _message + "<t align='left'>      1. For example, if a player has been a victim of robbery or murder, that player <t color='#ff0000'>may not return</t> to seek revenge and send him/her to jail.<br/></t>";
_message = _message + "<t align='left'>      2. You may not follow a player around who is breaking the law and continue to collect the bounty on them over and over.<br/></t>";
_message = _message + "<t align='left'>            1. Example a player is placed in jail, <t color='#ff0000'>you or a member of your group</t> break them out only so you can place them back into jail over and over.<br/></t>";
_message = _message + "<t align='left'>      3. You may not group with a player and allow then to build a bounty just to collect on it in the end.<br/></t>";
_message = _message + "<t align='left'>_________________________________________</t><br/><br/>";
_message = _message + "<t align='left'>4. Do NOT abuse the vigilante license.<br/></t>";
_message = _message + "<t align='left'>      1. This includes unrestraining your friends or others that an officer has restrained. <t color='#ff0000'>You are a Vigilante not a Rebel</t>.<br/></t>";
_message = _message + "<t align='left'>      2. Do NOT send players to jail with <t color='#ff0000'>less than $75,000 bounty</t>.<br/></t>";
_message = _message + "<t align='left'>      3. Do NOT take a wanted player, <t color='#ff0000'>that is already restrained</t>, away from another Vigilante unless they have asked you to.<br/></t>";
_message = _message + "<t align='left'>      4. Do NOT pull players out of vehicles if a player is not wanted.<br/></t>";
_message = _message + "<t align='left'>      5. Do NOT send people from your own gang to jail.<br/></t>";
_message = _message + "<t align='left'>      6. You may NOT rob a wanted player and then place them in jail for a bounty.<br/></t>";
_message = _message + "<t align='left'>            1. This includes members of your party as well.<br/></t>";
_message = _message + "<t align='left'>_________________________________________</t><br/><br/>";
_message = _message + "<t align='left'>5. Vigilantes that repeatedly down another player without justifiable cause will be considered as RDM and will be dealt with.<br/></t>";
_message = _message + "<t align='left'>      1. Downing a player to keep them down cause you have no zipties is RDM.<br/></t>";
_message = _message + "<t align='left'>      2. Downing a player breaking the law and not on the wanted list is RDM.<br/></t>";
_message = _message + "<t align='left'>            1. If you are robbing a player outside the vigilante role it must be RP'd as normal or it is RDM.<br/></t>";
_message = _message + "<t align='left'>      3. Downing a player with a bounty before announcing yourself is RDM (Side chat is not considered announcing).<br/></t>";
_message = _message + "<t align='left'>      4. Downing a player with a bounty that is running away from you after announcing yourself is NOT RDM.<br/></t>";
_message = _message + "<t align='left'>_________________________________________</t><br/><br/>";
_message = _message + "<t align='left'>6. Vigilantes can simply be put as -forces that have been rejected, denied or have yet to be accepted to the Police Force to serve justice in Altis Life-. However, they are not cops. Hence, they are only authorized to act upon subjects that have a $75,000 bounty or higher on their head. You do NOT enforce laws, that is an officers job.<br/></t>";
_message = _message + "<t align='left'>      1. Vigilantes may not have their weapons out in main cities just like any other Civilian. If a APD member asks a vigilante to holster his/her weapon, he/she must correspond with appropriate actions to avoid prosecution.<br/></t>";
_message = _message + "<t align='left'>      2. Vigilantes should never vigi unrestrain or attempt to arrest an APD Detainee. If APD ask for assistance that is fine but do not run into an area yelling -I am a vigilante and attempt- to arrest someone the officer is dealing with.<br/></t>";
_message = _message + "<t align='left'>      3. If you are escorting a player with a bounty and an officer asks you to stop, you need to stop and explain what you are doing. Officers can NOT take the wanted player away and attempt to ticket or send the player to jail if you have captured the wanted player(s) in the appropriate manner.<br/></t>";
_message = _message + "<t align='left'>            1. If you are wanted the APD may process you and if you refuse to pay your ticket the APD may then process all wanted players accordingly.<br/></t>";
_message = _message + "<t align='left'>      4. If you <t color='#ff0000'>restrain a wanted player</t> you are required to:<br/></t>";
_message = _message + "<t align='left'>            1. Let the player know what they are wanted for.<br/></t>";
_message = _message + "<t align='left'>            2. Let the player know their bounty amount.<br/></t>";
_message = _message + "<t align='left'>            3. Let the player know you will be sending them to jail.<br/></t>";
_message = _message + "<t align='left'>            4. Take the player to the jail transport. (The vigilante Outposts (get close to the map marker location))<br/></t>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#ffff1a'>5.If multiple vigilantes engage a suspect, the vigilante that tases may make the arrest.</t><br/><br/>";
_message = _message + "<t align='left' size='1.2' shadow='0'><t color='#73e600'>_______________ Vigilante Tier Rewards _______________</t><br/><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#00ffbf'>Vigilante Tier 1 - 0 Arrests</t><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#00ffbf'>Equipment : P07</t><br/>";
_message = _message + "<t align='left' size='1.2' shadow='0'><t color='#5252ff'>______________________________</t><br/><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#bf00ff'>Vigilante Tier 2 - 25 Arrests</t><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#bf00ff'>Equipment : P07, ACP-C2, Sting</t><br/>";
_message = _message + "<t align='left' size='1.2' shadow='0'><t color='#5252ff'>______________________________</t><br/><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#ff00bf'>Vigilante Tier 3 - 50 Arrests</t><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#ff00bf'>Equipment : P07, ACP-C2, Sting, T3 Vest</t><br/>";
_message = _message + "<t align='left' size='1.2' shadow='0'><t color='#5252ff'>______________________________</t><br/><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#40ff00'>Vigilante Tier 4 - 100 Arrests</t><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#40ff00'>Equipment : P07, ACP-C2, Sting, T3 Vest, SPAR16, SPAR16-GL</t><br/>";
_message = _message + "<t align='left' size='1.2' shadow='0'><t color='#5252ff'>______________________________</t><br/><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#ff1a53'>Vigilante Tier 5 - 200 Arrests</t><br/>";
_message = _message + "<t align='left' size='1.1' shadow='0'><t color='#ff1a53'>Equipment : P07, ACP-C2, Sting, T3 Vest, SPAR16, SPAR16-GL, SPAR16S</t><br/>";
_message = _message + "<t align='left' size='1' shadow='0'><t color='#ff1a53'>Ability : Spawn Athira Vigi </t><br/>";
_message = _message + "<t align='left' size='2.5' shadow='0'><t color='#73e600'>____________________________________</t><br/><br/>";



//Fill only the first text
_text1 ctrlSetStructuredText (parseText _message);
_title ctrlSetText "EdenRP Vigilante Rules and Information";

//Resize StructuredText component to display the scrollbar if needed
_positionText1 = ctrlPosition _text1;
_yText1 = _positionText1 select 1;
_hText1 = ctrlTextHeight _text1;
_text1 ctrlSetPosition [_positionText1 select 0, _yText1, _positionText1 select 2, _hText1];
_text1 ctrlCommit 0;
//Hide second text, spoiler text and button
_buttonSpoiler ctrlSetFade 1;
_buttonSpoiler ctrlCommit 0;
_buttonSpoiler ctrlEnable false;
_textSpoiler ctrlSetFade 1;
_textSpoiler ctrlCommit 0;
_text2 ctrlSetFade 1;
_text2 ctrlCommit 0;
