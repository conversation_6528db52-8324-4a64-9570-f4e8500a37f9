// File: fn_xmasTrees.sqf
// Author: <PERSON> "tkc<PERSON><PERSON>" Schultz

//private _planter = "Land_TreeBin_F" createVehicleLocal [3646.16,13099.7,9.53674]; // Kavala
//private _planterO = "Land_TreeBin_F" createVehicleLocal [14052.5,18760.4,9.53674]; // Athira
//private _planterT = "Land_TreeBin_F" createVehicleLocal [16771.9,12543.1,9.53674]; // Pyrgos
//private _planterE = "Land_TreeBin_F" createVehicleLocal [25706.3,21367.6,9.53674]; // Sofia
private _simpleTreeOne = createSimpleObject ["a3\plants_f\tree\t_pinuss1s_f.p3d",[3646.16,13099.7,13.4]];
_simpleTreeOne setDir 0;
private _simpleTreeTwo = createSimpleObject ["a3\plants_f\tree\t_pinuss1s_f.p3d",[3646.16,13099.7,13.4]];
_simpleTreeTwo setDir 305;

private _simpleTreeThree = createSimpleObject ["a3\plants_f\tree\t_pinuss1s_f.p3d",[14052.5,18760.4,28.7]];
_simpleTreeThree setDir 0;
private _simpleTreeFour = createSimpleObject ["a3\plants_f\tree\t_pinuss1s_f.p3d",[14052.5,18760.4,28.7]];
_simpleTreeFour setDir 305;

private _simpleTreeSeven = createSimpleObject ["a3\plants_f\tree\t_pinuss1s_f.p3d",[16771.9,12543.1,18.8]];
_simpleTreeSeven setDir 0;
private _simpleTreeEight = createSimpleObject ["a3\plants_f\tree\t_pinuss1s_f.p3d",[16771.9,12543.1,18.8]];
_simpleTreeEight setDir 305;

private _simpleTreeNine = createSimpleObject ["a3\plants_f\tree\t_pinuss1s_f.p3d",[25706.3,21367.6,23.9]];
_simpleTreeNine setDir 0;
private _simpleTreeTen = createSimpleObject ["a3\plants_f\tree\t_pinuss1s_f.p3d",[25706.3,21367.6,23.9]];
_simpleTreeTen setDir 305;

private _color = [
    "images\ornOne.jpg",
    "images\ornTwo.jpg",
    "images\ornThree.jpg",
    "images\ornFour.jpg",
    "images\ornFive.jpg"
];

{
	private _ornament = "Sign_Sphere10cm_F" createVehicleLocal [200,200,200];
	_ornament attachTo [_simpleTreeOne,_x];
	_ornament setObjectMaterial [0,"\a3\data_f\default.rvmat"];
	_ornament setObjectTexture [0, selectRandom _color];
} forEach [
	[-1.17969,1.35156,-2.60407],
	[1.28931,1.44141,-2.59533],
	[-0.51123,-1.80273,-0.0691557],
	[-1.5,-1.36621,-0.299796],
	[-1.13013,-1.80371,-0.674389],
	[-1.72046,-0.888672,-0.666501],
	[-1.6687,-1.46875,-1.34798],
	[-0.51709,-0.983398,-0.0557909],
	[-1.1543,-1.06055,0.576214],
	[-1.56616,-0.225586,0.523792],
	[-0.635254,-0.0761719,1.24977],
	[-1.9707,0.418945,-0.494906],
	[-1.58252,-0.350586,-1.0069],
	[-1.46851,0.435547,0.228078],
	[-1.40698,-0.419922,0.0196924],
	[-0.98877,-1.70996,-1.63532],
	[-0.297363,-2.06445,-1.5495],
	[0.227783,-2.28906,-1.29478],
	[-0.394531,2.12891,-2.15448],
	[-1.2334,1.17969,0.591312],
	[-1.11572,0.279297,1.23087],
	[-0.473877,1.03711,1.25945],
	[-0.55127,0.542969,1.28878],
	[-1.82568,1.08398,-0.466783],
	[-1.65747,0.664063,-1.23857],
	[-1.57983,1.57813,-1.15648],
	[-1.33789,1.49316,-0.463603],
	[-0.540527,1.33398,0.156194],
	[0.961914,1.61426,-0.189494],
	[-0.565918,1.81152,-0.208119],
	[-0.0327148,1.58105,-0.053195],
	[0.302734,1.55566,0.396802],
	[0.774902,0.461914,1.15753],
	[0.657471,1,0.68532],
	[1.46484,0.994141,-0.219485],
	[1.38843,0.608398,0.336154],
	[1.29565,0.0263672,0.788883],
	[0.73584,-0.626953,1.18414],
	[0.747559,-0.0576172,1.3866],
	[1.64258,-0.438477,-0.0878296],
	[1.71436,0.166016,-0.492693],
	[1.93945,0.474609,-0.895686],
	[1.27905,1.01172,-1.59657],
	[1.43213,-0.400391,-1.56482],
	[1.61792,-0.957031,-0.378121],
	[1.22095,-0.605469,0.291803],
	[1.04858,1.16797,0.137674],
	[1.92212,-0.523438,-1.06875],
	[0.789795,-1.52832,-1.21474],
	[1.64697,-1.24121,-2.32045],
	[0.985107,-1.19434,-2.5709],
	[0.352295,-1.5957,-2.33073],
	[-0.973389,-1.84375,-2.08793],
	[-1.16479,-1.3457,-2.35934],
	[-2.25537,-0.589844,-2.08635],
	[-1.7019,-0.998047,-2.24104],
	[-1.81714,0.757813,-2.26974],
	[-1.53564,0.245117,-2.57398],
	[-0.951904,1.8418,-2.35021],
	[0.180664,1.96875,-2.32359],
	[0.721191,1.75195,-1.12386],
	[0.191895,1.88867,-1.18792],
	[1.06323,0.978516,-0.598044],
	[1.92676,1.30176,-2.27261],
	[2.18994,-0.258789,-2.364],
	[0.0651855,-1.41211,0.380757],
	[-0.313721,-1.55566,0.10895],
	[1.18799,-1.40723,-0.128509],
	[0.775146,-1.76465,-0.472126],
	[-0.523682,-1.47754,-0.854243]
];

{
	private _ornament = "Sign_Sphere10cm_F" createVehicleLocal [200,200,200];
	_ornament attachTo [_simpleTreeThree,_x];
	_ornament setObjectMaterial [0,"\a3\data_f\default.rvmat"];
	_ornament setObjectTexture [0, selectRandom _color];
} forEach [
	[-1.17969,1.35156,-2.60407],
	[1.28931,1.44141,-2.59533],
	[-0.51123,-1.80273,-0.0691557],
	[-1.5,-1.36621,-0.299796],
	[-1.13013,-1.80371,-0.674389],
	[-1.72046,-0.888672,-0.666501],
	[-1.6687,-1.46875,-1.34798],
	[-0.51709,-0.983398,-0.0557909],
	[-1.1543,-1.06055,0.576214],
	[-1.56616,-0.225586,0.523792],
	[-0.635254,-0.0761719,1.24977],
	[-1.9707,0.418945,-0.494906],
	[-1.58252,-0.350586,-1.0069],
	[-1.46851,0.435547,0.228078],
	[-1.40698,-0.419922,0.0196924],
	[-0.98877,-1.70996,-1.63532],
	[-0.297363,-2.06445,-1.5495],
	[0.227783,-2.28906,-1.29478],
	[-0.394531,2.12891,-2.15448],
	[-1.2334,1.17969,0.591312],
	[-1.11572,0.279297,1.23087],
	[-0.473877,1.03711,1.25945],
	[-0.55127,0.542969,1.28878],
	[-1.82568,1.08398,-0.466783],
	[-1.65747,0.664063,-1.23857],
	[-1.57983,1.57813,-1.15648],
	[-1.33789,1.49316,-0.463603],
	[-0.540527,1.33398,0.156194],
	[0.961914,1.61426,-0.189494],
	[-0.565918,1.81152,-0.208119],
	[-0.0327148,1.58105,-0.053195],
	[0.302734,1.55566,0.396802],
	[0.774902,0.461914,1.15753],
	[0.657471,1,0.68532],
	[1.46484,0.994141,-0.219485],
	[1.38843,0.608398,0.336154],
	[1.29565,0.0263672,0.788883],
	[0.73584,-0.626953,1.18414],
	[0.747559,-0.0576172,1.3866],
	[1.64258,-0.438477,-0.0878296],
	[1.71436,0.166016,-0.492693],
	[1.93945,0.474609,-0.895686],
	[1.27905,1.01172,-1.59657],
	[1.43213,-0.400391,-1.56482],
	[1.61792,-0.957031,-0.378121],
	[1.22095,-0.605469,0.291803],
	[1.04858,1.16797,0.137674],
	[1.92212,-0.523438,-1.06875],
	[0.789795,-1.52832,-1.21474],
	[1.64697,-1.24121,-2.32045],
	[0.985107,-1.19434,-2.5709],
	[0.352295,-1.5957,-2.33073],
	[-0.973389,-1.84375,-2.08793],
	[-1.16479,-1.3457,-2.35934],
	[-2.25537,-0.589844,-2.08635],
	[-1.7019,-0.998047,-2.24104],
	[-1.81714,0.757813,-2.26974],
	[-1.53564,0.245117,-2.57398],
	[-0.951904,1.8418,-2.35021],
	[0.180664,1.96875,-2.32359],
	[0.721191,1.75195,-1.12386],
	[0.191895,1.88867,-1.18792],
	[1.06323,0.978516,-0.598044],
	[1.92676,1.30176,-2.27261],
	[2.18994,-0.258789,-2.364],
	[0.0651855,-1.41211,0.380757],
	[-0.313721,-1.55566,0.10895],
	[1.18799,-1.40723,-0.128509],
	[0.775146,-1.76465,-0.472126],
	[-0.523682,-1.47754,-0.854243]
];

{
	private _ornament = "Sign_Sphere10cm_F" createVehicleLocal [200,200,200];
	_ornament attachTo [_simpleTreeSeven,_x];
	_ornament setObjectMaterial [0,"\a3\data_f\default.rvmat"];
	_ornament setObjectTexture [0, selectRandom _color];
} forEach [
	[-1.17969,1.35156,-2.60407],
	[1.28931,1.44141,-2.59533],
	[-0.51123,-1.80273,-0.0691557],
	[-1.5,-1.36621,-0.299796],
	[-1.13013,-1.80371,-0.674389],
	[-1.72046,-0.888672,-0.666501],
	[-1.6687,-1.46875,-1.34798],
	[-0.51709,-0.983398,-0.0557909],
	[-1.1543,-1.06055,0.576214],
	[-1.56616,-0.225586,0.523792],
	[-0.635254,-0.0761719,1.24977],
	[-1.9707,0.418945,-0.494906],
	[-1.58252,-0.350586,-1.0069],
	[-1.46851,0.435547,0.228078],
	[-1.40698,-0.419922,0.0196924],
	[-0.98877,-1.70996,-1.63532],
	[-0.297363,-2.06445,-1.5495],
	[0.227783,-2.28906,-1.29478],
	[-0.394531,2.12891,-2.15448],
	[-1.2334,1.17969,0.591312],
	[-1.11572,0.279297,1.23087],
	[-0.473877,1.03711,1.25945],
	[-0.55127,0.542969,1.28878],
	[-1.82568,1.08398,-0.466783],
	[-1.65747,0.664063,-1.23857],
	[-1.57983,1.57813,-1.15648],
	[-1.33789,1.49316,-0.463603],
	[-0.540527,1.33398,0.156194],
	[0.961914,1.61426,-0.189494],
	[-0.565918,1.81152,-0.208119],
	[-0.0327148,1.58105,-0.053195],
	[0.302734,1.55566,0.396802],
	[0.774902,0.461914,1.15753],
	[0.657471,1,0.68532],
	[1.46484,0.994141,-0.219485],
	[1.38843,0.608398,0.336154],
	[1.29565,0.0263672,0.788883],
	[0.73584,-0.626953,1.18414],
	[0.747559,-0.0576172,1.3866],
	[1.64258,-0.438477,-0.0878296],
	[1.71436,0.166016,-0.492693],
	[1.93945,0.474609,-0.895686],
	[1.27905,1.01172,-1.59657],
	[1.43213,-0.400391,-1.56482],
	[1.61792,-0.957031,-0.378121],
	[1.22095,-0.605469,0.291803],
	[1.04858,1.16797,0.137674],
	[1.92212,-0.523438,-1.06875],
	[0.789795,-1.52832,-1.21474],
	[1.64697,-1.24121,-2.32045],
	[0.985107,-1.19434,-2.5709],
	[0.352295,-1.5957,-2.33073],
	[-0.973389,-1.84375,-2.08793],
	[-1.16479,-1.3457,-2.35934],
	[-2.25537,-0.589844,-2.08635],
	[-1.7019,-0.998047,-2.24104],
	[-1.81714,0.757813,-2.26974],
	[-1.53564,0.245117,-2.57398],
	[-0.951904,1.8418,-2.35021],
	[0.180664,1.96875,-2.32359],
	[0.721191,1.75195,-1.12386],
	[0.191895,1.88867,-1.18792],
	[1.06323,0.978516,-0.598044],
	[1.92676,1.30176,-2.27261],
	[2.18994,-0.258789,-2.364],
	[0.0651855,-1.41211,0.380757],
	[-0.313721,-1.55566,0.10895],
	[1.18799,-1.40723,-0.128509],
	[0.775146,-1.76465,-0.472126],
	[-0.523682,-1.47754,-0.854243]
];

{
	private _ornament = "Sign_Sphere10cm_F" createVehicleLocal [200,200,200];
	_ornament attachTo [_simpleTreeNine,_x];
	_ornament setObjectMaterial [0,"\a3\data_f\default.rvmat"];
	_ornament setObjectTexture [0, selectRandom _color];
} forEach [
	[-1.17969,1.35156,-2.60407],
	[1.28931,1.44141,-2.59533],
	[-0.51123,-1.80273,-0.0691557],
	[-1.5,-1.36621,-0.299796],
	[-1.13013,-1.80371,-0.674389],
	[-1.72046,-0.888672,-0.666501],
	[-1.6687,-1.46875,-1.34798],
	[-0.51709,-0.983398,-0.0557909],
	[-1.1543,-1.06055,0.576214],
	[-1.56616,-0.225586,0.523792],
	[-0.635254,-0.0761719,1.24977],
	[-1.9707,0.418945,-0.494906],
	[-1.58252,-0.350586,-1.0069],
	[-1.46851,0.435547,0.228078],
	[-1.40698,-0.419922,0.0196924],
	[-0.98877,-1.70996,-1.63532],
	[-0.297363,-2.06445,-1.5495],
	[0.227783,-2.28906,-1.29478],
	[-0.394531,2.12891,-2.15448],
	[-1.2334,1.17969,0.591312],
	[-1.11572,0.279297,1.23087],
	[-0.473877,1.03711,1.25945],
	[-0.55127,0.542969,1.28878],
	[-1.82568,1.08398,-0.466783],
	[-1.65747,0.664063,-1.23857],
	[-1.57983,1.57813,-1.15648],
	[-1.33789,1.49316,-0.463603],
	[-0.540527,1.33398,0.156194],
	[0.961914,1.61426,-0.189494],
	[-0.565918,1.81152,-0.208119],
	[-0.0327148,1.58105,-0.053195],
	[0.302734,1.55566,0.396802],
	[0.774902,0.461914,1.15753],
	[0.657471,1,0.68532],
	[1.46484,0.994141,-0.219485],
	[1.38843,0.608398,0.336154],
	[1.29565,0.0263672,0.788883],
	[0.73584,-0.626953,1.18414],
	[0.747559,-0.0576172,1.3866],
	[1.64258,-0.438477,-0.0878296],
	[1.71436,0.166016,-0.492693],
	[1.93945,0.474609,-0.895686],
	[1.27905,1.01172,-1.59657],
	[1.43213,-0.400391,-1.56482],
	[1.61792,-0.957031,-0.378121],
	[1.22095,-0.605469,0.291803],
	[1.04858,1.16797,0.137674],
	[1.92212,-0.523438,-1.06875],
	[0.789795,-1.52832,-1.21474],
	[1.64697,-1.24121,-2.32045],
	[0.985107,-1.19434,-2.5709],
	[0.352295,-1.5957,-2.33073],
	[-0.973389,-1.84375,-2.08793],
	[-1.16479,-1.3457,-2.35934],
	[-2.25537,-0.589844,-2.08635],
	[-1.7019,-0.998047,-2.24104],
	[-1.81714,0.757813,-2.26974],
	[-1.53564,0.245117,-2.57398],
	[-0.951904,1.8418,-2.35021],
	[0.180664,1.96875,-2.32359],
	[0.721191,1.75195,-1.12386],
	[0.191895,1.88867,-1.18792],
	[1.06323,0.978516,-0.598044],
	[1.92676,1.30176,-2.27261],
	[2.18994,-0.258789,-2.364],
	[0.0651855,-1.41211,0.380757],
	[-0.313721,-1.55566,0.10895],
	[1.18799,-1.40723,-0.128509],
	[0.775146,-1.76465,-0.472126],
	[-0.523682,-1.47754,-0.854243]
];