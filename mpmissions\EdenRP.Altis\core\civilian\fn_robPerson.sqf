//  File: fn_rob<PERSON>erson.sqf
//	Author: <PERSON> "<PERSON>" Boardwine

//	Description: Getting tired of adding descriptions...
private["_robber","_robbedValue","_robbedPlayer","_robbedUID"];
_robber = param [0,Obj<PERSON><PERSON>,[<PERSON>b<PERSON><PERSON><PERSON>]];
_robbedPlayer = player;
_robbedUID = getPlayerUID player;
if(isNull _robber) exitWith {};

if(eden_cash > 0) then {
	if((call eden_restrictions) || _robber getVariable ["restrictions", false]) then {
		_robbedValue = round(eden_cash * 0.5);
	} else {
		_robbedValue = eden_cash;
	};
	eden_cash = 0;
	eden_cache_cash = eden_random_cash_val;
	[0] call EDEN_fnc_ClupdatePartial;

	sleep 3;

	[[_robbedValue,_robbedPlayer,_robbedUID],"EDEN_fnc_robReceive",_robber,false] spawn EDEN_fnc_MP;
	[[getPlayerUID _robber,_robber getVariable["realname",name _robber],"8",_robber],"EDENS_fnc_wantedAdd",false,false] spawn EDEN_fnc_MP;
	[[1,"STR_NOTF_Robbed",true,[_robber getVariable["realname",name _robber],profileName,[_robbedValue] call EDEN_fnc_numberText]],"EDEN_fnc_broadcast",-2,false] spawn EDEN_fnc_MP;

	[
		["event", "Got Robbed"],
		["player", name player],
		["player_id", getPlayerUID player],
		["by", name _robber],
		["by_id", getPlayerUID _robber],
		["amount", _robbedValue],
		["position", getPos player]
	]	call EDEN_fnc_logIt;
}
	else
{
	[[2,"STR_NOTF_RobFail",true,[profileName]],"EDEN_fnc_broadcast",_robber,false] spawn EDEN_fnc_MP;
};
