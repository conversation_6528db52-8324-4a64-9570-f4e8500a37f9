//  File: fn_claimGangVeh.sqf
//	Author: <PERSON> "tkc<PERSON>" Schultz
// 	Modified by: Kurt
//	Description: Used for adding vehicles to the gang's garage.

params [
	["_building",objNull,[objNull]]
];
private ["_esc","_vehName","_vehicle","_nearVehicles","_vehData","_vehOwner"];

_esc = false;
if (isNull _building) exitWith {};
if !(playerSide isEqualTo civilian) exitWith {};
if (vehicle player != player) exitWith {};

_vehicle = objNull;
oev_garageCount = -1;
[player] remoteExec ["OES_fnc_checkGangVehicleLimit",2];
waitUntil {!(oev_garageCount isEqualTo -1)};

_nearVehicles = nearestObjects[getPos (_this select 0),["Car","Truck","Armored"],30]; //Fetch vehicles within 30m.
if (count _nearVehicles > 0) then {
	{
		//Does the vehicle belong to a gang?
		if ((_x getVariable ["gangID",0]) isEqualTo 0) then {
			//Is the vehicle an unclaimed BW vehicle?
			if (_x getVariable "isBlackwater") exitWith {_esc = true;};
			//Is the vehicle an escort vehicle?
			if (_x getVariable ["isEscort",false]) exitWith {_esc = true;};
			//Does the person own the vehicle?
			_vehData = _x getVariable["vehicle_info_owners",[]];
			if(count _vehData  > 0) then {
				_vehOwner = (_vehData select 0) select 0;
				if((getPlayerUID player) == _vehOwner) exitWith	{
					_vehicle = _x;
				};
			};
		};
	} forEach _nearVehicles;
};
if(oev_garageCount >= 25) exitWith {hint "Your gang garage is full and can't hold any more vehicles!";};
if (_esc) exitWith {hint "This is an unowned vehicle!";};
if (isNull _vehicle) exitWith {hint "You don't own any nearby vehicle!"};
_vehName = getText(configFile >> "CfgVehicles" >> (typeof _vehicle) >> "displayName");
if (_vehicle getVariable ["rekey",false]) exitWith {hint "This vehicle is already being added to your gang shed!";};
//Confirmation
private _action = [
	format["Are you sure you want to add your %1 to the gang garage?", _vehName],
	"Add Gang Vehicle",
	"Yes",
	"No"
] call BIS_fnc_GUImessage;

if (_action) then {
	if (_vehicle getVariable ["rekey",false]) exitWith {hint "This vehicle is already being added to your gang shed!";};
	oev_action_inUse = true;
	life_claim_done = false;
	life_claim_success = false;
	hint "Replacing vehicle locks...";

	[_vehicle,player,(oev_gang_data select 0)] remoteExec ["OES_fnc_gangClaim",2];

	waitUntil {life_claim_done};

	oev_action_inUse = false;
	if !(life_claim_success) exitWith {
		life_claim_done = false;
		life_claim_success = false;
		hint "The vehicle could not be transferred to the gang garage at this time.";
	};

	life_claim_success = false;
	life_claim_done = false;
	hint format ["Your %1 has been added to your gang's garage!",_vehName];
};