if((_this select 0) isEqualType 0) exitWith {
  6 cutRsc ["life_timer","PLAIN DOWN"];
  _display = uiNamespace getVariable "life_timer";
  _timer = _display displayCtrl 38301;
  _timer ctrlSetTextColor [0,242,255,1];
  _time = time + 420;
  if(_this select 0 == 0) then {
    _time = time + 300;
  };
  if(_this select 1) then {
    _time = time + (_this select 0);
  };
  while {true} do {
    if (isNull _display) then {
      6 cutRsc ["life_timer","PLAIN DOWN"];
      _display = uiNamespace getVariable "life_timer";
      _timer = _display displayCtrl 38301;
      _timer ctrlSetTextColor [0,242,255,1];
    };
    if (round(_time - time) < 1) exitWith {};
    if !(eden_artGallery) exitWith {};
    _timer ctrlSetText format["%1",[(_time - time),"MM:SS"] call BIS_fnc_secondsToString];
    uiSleep 0.09;
  };
  6 cutText ["","PLAIN DOWN"];
};

if(eden_artGallery) exitWith {hint "Someone else is already robbing the Art Gallery!";};
if(eden_action_inUse) exitWith {hint "Please finish what you are doing first!";};
if(eden_robPaintingCD > time) exitWith {hint "Please wait 5 minutes before attempting to rob another painting!";};
if([west,2] call EDEN_fnc_playerCount < 5) exitWith {hint "There must be 5 or more cops online to rob the Art Gallery!";};
if(currentWeapon player isEqualTo "" || currentMagazine player in ["30Rnd_9x21_Mag","16Rnd_9x21_Mag","11Rnd_45ACP_Mag","30Rnd_9x21_Mag_SMG_02","6Rnd_45ACP_Cylinder","9Rnd_45ACP_Mag"]) exitWith {hint "You need a 5.56 caliber gun or higher!";};
if(currentMagazine player isEqualTo "") exitWith {hint "You need ammo!";};
if((altis_bank getVariable ["chargeplaced",false]) || (altis_bank_1 getVariable ["chargeplaced",false]) || (altis_bank_2 getVariable ["chargeplaced",false])) exitWith {hint "You cannot rob the Art Gallery while a bank is active!";};
_bwBldg = nearestObject [[20898.6,19221.7,0.********],"Land_Dome_Big_F"];
if((_bwBldg getVariable ["chargeplaced",false]) || (fed_bank getVariable ["chargeplaced",false]) || (jailwall getVariable ["chargeplaced",false])) exitWith {hint "You cannot rob the Art Gallery while a federal event is active!";};

_itemName = "";
_size = _this select 3;
_painting = _this select 0;
_cpRate = 0.00064;
if(_size == 0) then {
  _itemName = "paintingSm";
  _cpRate = 0.00088;
} else {
  _itemName = "paintingLg";
};
_diff = [_itemName,1,eden_carryWeight,eden_maxWeight] call EDEN_fnc_calWeightDiff;
if(_diff <= 0) exitWith {hint "You do not have enough inventory space to hold this painting!";};

eden_artgallery = true;
publicVariable "eden_artgallery";

[3,"<t color='#ff2222'><t size='2.2'><t align='center'>Art Gallery<br/><t color='#FFC966'><t align='center'><t size='1.2'>The Kavala Art Gallery is being robbed for one of its priceless paintings!",false,[]] remoteExec["EDEN_fnc_broadcast",-2,false];
[_size,false] remoteExec["EDEN_fnc_robPainting",(playableUnits select {side _x == west || group _x isEqualTo group player})];
[gallery_siren,"galleryAlarm"] remoteExec["EDEN_fnc_say3D",((position player nearEntities ["Man", 50]) select {isPlayer _x})];
[0,player,_size] remoteExec["EDENS_fnc_artGallery",2];

disableSerialization;
_title = "Stealing Painting";
5 cutRsc["life_progress","PLAIN DOWN"];
_ui = uiNamespace getVariable "life_progress";
_progressBar = _ui displayCtrl 38201;
_titleText = _ui displayCtrl 38202;
_titleText ctrlSetText format["%2 (1%1)...","%",_title];
_progressBar progressSetPosition 0.01;
_cP = 0.01;

_exit = false;
while {true} do {
	uiSleep 0.26;
	if(isNull _ui) then {
		5 cutRsc ["life_progress","PLAIN DOWN"];
		_ui = uiNamespace getVariable "life_progress";
		_progressBar = _ui displayCtrl 38201;
		_titleText = _ui displayCtrl 38202;
	};
	_cP = _cP + _cpRate;
	_progressBar progressSetPosition _cP;
	_titleText ctrlSetText format["%3 (%1%2)...",round(_cP * 100),"%",_title];
	if (_cP >= 1) exitWith {};
  if !(alive player) exitWith {
    _exit = true;
  };
  if (!(isNull objectParent player) || player getVariable["restrained",false]) exitWith {
    _exit = true;
    hint "You must stay near the painting to steal it!";
  };
  if (player distance _painting > 15) exitWith {
    _exit = true;
    hint "You must stay within 15m of the painting to steal it!";
  };
};
5 cutText["","PLAIN DOWN"];

if(_exit) exitWith {
  eden_artgallery = false;
  publicVariable "eden_artgallery";
  if(eden_numRobAttempted >= 1) then {
    eden_numRobAttempted = 0;
    eden_robPaintingCD = time+300;
  } else {
    eden_numRobAttempted = eden_numRobAttempted+1;
  };
};

eden_numRobAttempted = 0;
_diff = [_itemName,1,eden_carryWeight,eden_maxWeight] call EDEN_fnc_calWeightDiff;
if(_diff <= 0) exitWith {hint "You do not have enough inventory space to hold this painting!";};

_painting setVariable["cooldown",true,true];
[3,"<t color='#ff2222'><t size='2.2'><t align='center'>Art Gallery<br/><t color='#FFC966'><t align='center'><t size='1.2'>A painting has been stolen from the Kavala Art Gallery!",false,[]] remoteExec["EDEN_fnc_broadcast",-2,false];
[1,_painting,(getObjectTextures _painting) select 0,player,_size] remoteExec["EDENS_fnc_artGallery",2];
_painting setObjectTextureGlobal[0, ""];

[true,_itemName,1] call EDEN_fnc_handleInv;

eden_artgallery = false;
publicVariable "eden_artgallery";
