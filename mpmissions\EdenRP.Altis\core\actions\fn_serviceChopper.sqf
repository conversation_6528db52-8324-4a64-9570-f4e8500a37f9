//  File: fn_serviceChopper.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Main functionality for the chopper service paid, to be replaced in later version.
disableSerialization;
private["_search","_ui","_progress","_cP","_pgText","_title"];

if(isNil "eden_cash") then {eden_cash = 0; eden_cache_cash = eden_random_cash_val;};
if(isNil "eden_atmcash") then {eden_atmcash = 0; eden_cache_atmcash = eden_random_cash_val;};

if((eden_cash + (eden_random_cash_val - 5000)) > eden_cache_cash || (eden_atmcash + (eden_random_cash_val - 5000)) > eden_cache_atmcash) exitWith {
	[["event","Hacked Cash"],["player",name player],["player_id",getPlayerUID player],["hackedcash",eden_cash - (eden_cache_cash - eden_random_cash_val)],["hackedbank",eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)],["location",getPosATL player]] call EDEN_fnc_logIt;
	[[profileName,format["Hacked Cash Detected! (Cash Hacked In = %1) (Bank Hacked In = %2)",eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDEN_fnc_notifyAdmins",-2,false] spawn EDEN_fnc_MP;
	[[1,player,[eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDENS_fnc_handleDisc",false,false] spawn EDEN_fnc_MP;
	["HackedMoney",false,false] call compile PreProcessFileLineNumbers "\a3\functions_f\Misc\fn_endMission.sqf";
};

if(eden_action_inUse) exitWith {hint localize "STR_NOTF_Action"};
_search = nearestObjects[getPos air_sp, ["Air"],5];
if(count _search == 0) exitWith {hint localize "STR_Service_Chopper_NoAir"};
if(eden_atmcash < 1000 && eden_cash < 1000) exitWith {hint localize "STR_Serive_Chopper_NotEnough"};

if(eden_cash >= 1000) then {
	eden_cash = eden_cash - 1000;
	eden_cache_cash = eden_cache_cash - 1000;
}else{
	eden_atmcash = eden_atmcash - 1000;
	eden_cache_atmcash = eden_cache_atmcash - 1000;
};

eden_action_inUse = true;
_title = localize "STR_Service_Chopper_Servicing";
5 cutRsc ["life_progress","PLAIN DOWN"];
_ui = uiNameSpace getVariable "life_progress";
_progress = _ui displayCtrl 38201;
_pgText = _ui displayCtrl 38202;
_pgText ctrlSetText format["%2 (1%1)...","%",_title];
_progress progressSetPosition 0.01;
_cP = 0.01;

while {true} do
{
	uiSleep  0.2;
	_cP = _cP + 0.01;
	_progress progressSetPosition _cP;
	_pgText ctrlSetText format["%2 (%1%2)...",round(_cP * 100),"%",_title];
	if(_cP >= 1) exitWith {};
};

if(!alive (_search select 0) || (_search select 0) distance air_sp > 10) exitWith {eden_action_inUse = false; hint localize "STR_Service_Chopper_Missing"};
if(!local (_search select 0)) then
{
	[[(_search select 0),1],"EDEN_fnc_setFuel",(_search select 0),false] spawn EDEN_fnc_MP;
}
	else
{
	(_search select 0) setFuel 1;
};
_dam_obj = (_search select 0);
_dam_obj setDamage 0;

5 cutText ["","PLAIN DOWN"];
titleText [localize "STR_Service_Chopper_Done","PLAIN DOWN"];
eden_action_inUse = false;
