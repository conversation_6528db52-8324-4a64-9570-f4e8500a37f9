//	Filename: fn_removeKidney.sqf
//	Author: Serpico
//	Description: Remove a kidney from give player

private ["_upp","_progress","_pgText","_cP","_sleepVal","_victimName","_targetPlayer"];
_targetPlayer = cursorObject;
closeDialog 0;
eden_interrupted = false;
if (isNull _targetPlayer) exitWith {};
if (side _targetPlayer isEqualTo independent) exitWith {hint "You cannot harvest medic kidneys!";};
_victimName = _targetPlayer getVariable["realname",name _targetPlayer];
if (_targetPlayer getVariable ["kidneyRemoved",false]) exitWith {hint format["%1 has already had their kidney removed",_victimName]};
if (!(isNil {_targetPlayer getVariable "lastHarvest"}) && (((_targetPlayer getVariable ["lastHarvest",0]) + 1800) > serverTime)) exitWith {hint format ["This person has already had their kidney removed within the last 30 minutes."];};
if ((group player) isEqualTo (group _targetPlayer)) exitWith {
};
if ((eden_carryWeight + 10) > eden_maxWeight) exitWith {hint "You will not be able to carry this kidney!";};
if (!(_targetPlayer getVariable ["zipTied",false])) exitWith {hint "Your victim must be ziptied";};
if (life_inv_scalpel < 1) exitWith {hint "You do not have a scalpel";};

eden_action_inUse = true;
_targetPlayer setVariable ["kidneyHarvester",player,true];

//Setup our progress bar.
_upp = format["Removing kidney from %1",_victimName];
disableSerialization;
5 cutRsc ["life_progress","PLAIN DOWN"];
_ui = uiNameSpace getVariable "life_progress";
_progress = _ui displayCtrl 38201;
_pgText = _ui displayCtrl 38202;
_pgText ctrlSetText format["%2 (1%1)...","%",_upp];
_cP = 0;
_sleepVal = 0.27;

["AinvPknlMstpSnonWnonDnon_medic_1",1.5] spawn EDEN_fnc_handleAnim;

while {true} do {
	uiSleep _sleepVal;
	_cP = _cP + 0.01;
	_progress progressSetPosition _cP;
	_pgText ctrlSetText format["%3 (%1%2)...",round(_cP * 100),"%",_upp];
	if (_cP >= 1) exitWith {};
	if (!alive player) exitWith {eden_interrupted = true;};
	if (!alive _targetPlayer) exitWith {eden_interrupted = true;};
	if (player != vehicle player) exitWith {};
	if (eden_interrupted) exitWith {};
	if (life_inv_scalpel isEqualTo 0) exitWith {};
	if (_targetPlayer getVariable ["kidneyHarvester",objNull] != player) exitWith {};
};

eden_action_inUse = false;
5 cutText ["","PLAIN DOWN"];
[] spawn EDEN_fnc_handleAnim;
if (_targetPlayer getVariable ["kidneyHarvester",objNull] != player) exitWith {hint "Someone is already harvesting this players kidney!";};
_targetPlayer setVariable ["kidneyHarvester",nil,true];
if (life_inv_scalpel isEqualTo 0) exitWith {hint "You no longer have a scalpel";};
if (eden_interrupted) exitWith {eden_interrupted = false; titleText[localize "STR_NOTF_ActionCancel","PLAIN DOWN"]; eden_action_inUse = false;};
if (player != vehicle player) exitWith {titleText["You have cancelled your action by entering a vehicle","PLAIN DOWN"];};

[[_targetPlayer,"kick_balls"],"EDEN_fnc_say3D",-2,false] spawn EDEN_fnc_MP;
[[profileName],"EDEN_fnc_setKidneyStatus",_targetPlayer,FALSE] spawn EDEN_fnc_MP;
_targetPlayer setVariable ["lastHarvest",(serverTime + 1800),true];

[true,"kidney",1] call EDEN_fnc_handleInv;
[[getPlayerUID player,name player,"61",player],"EDENS_fnc_wantedAdd",false,false] spawn EDEN_fnc_MP;
[[0,"STR_NOTF_KidneyRemoved_1",true, [_victimName,name player]],"EDEN_fnc_broadcast",-2,false] spawn EDEN_fnc_MP;
[
	["event","Kidney Harvest"],
	["player",name player],
	["player_id",getPlayerUID player],
	["target",name _targetPlayer],
	["target_id",getPlayerUID _targetPlayer],
	["location",getPosATL player]
] call EDEN_fnc_logIt;

["kidney",1] call EDEN_fnc_statArrUp;
