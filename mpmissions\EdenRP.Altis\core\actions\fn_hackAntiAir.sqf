//	Author: Poseidon
//  File: fn_hackAntiAir.sqf
//	Description: Does what it do.

params [
	["_object",obj<PERSON>ull,[obj<PERSON>ull]]
];
private ["_perkTier","_cpRateChange"];

if (isNull _object) exitWith {};
if (playerSide isEqualTo independent) exitWith {};
if (playerSide isEqualTo civilian && {((_object getVariable ["virus",""]) isEqualTo "CIV")}) exitWith {hint "System is already infected with a virus."};
if (playerSide isEqualTo civilian && {!(_object getVariable["active",true])} && {((_object getVariable ["virus",""]) isEqualTo "")}) exitWith {hint "System is already disabled."};
//private _copCount = [west,2] call EDEN_fnc_playerCount;
//if (playerSide isEqualTo civilian && (_object isEqualTo fedAntiAir) && (_copCount < 5)) exitWith {hint localize "STR_Civ_NotEnoughCops";};
//if (playerSide isEqualTo civilian && (_object isEqualTo bwAntiAir) && (_copCount < 7)) exitWith {hint "There needs to be 7 or more cops online to continue.";};
if (playerSide isEqualTo civilian && {life_inv_hackingterminal isEqualTo 0} && {((_object getVariable ["virus",""]) isEqualTo "")}) exitWith {hint "You need a hacking terminal to disable the Anti-Air system!"};
_fin = false;
if (playerSide isEqualTo west && !(_object isEqualTo islandAntiAir_1)) then {
	if (playerSide isEqualTo west && (_object getVariable ["active",true]) && {((_object getVariable ["virus",""]) isEqualTo "")}) exitWith {hint "System is in working condition."; _fin = true};
	if (playerSide isEqualTo west && !(_object getVariable ["active",true]) && {((_object getVariable ["virus",""]) isEqualTo "APD")}) exitWith {hint "System is repairing."; _fin = true};
};
if (_fin) exitWith {};

private _position = getPos _object;
private _close = false;
disableSerialization;

eden_action_inUse = true;
5 cutRsc ["life_progress","PLAIN DOWN"];
private _ui = uiNamespace getVariable ["life_progress",displayNull];
private _progressBar = _ui displayCtrl 38201;
private _titleText = _ui displayCtrl 38202;
private _title = "Uploading virus";
private _abort = "Virus planting aborted due to moving too far from the device!";
if (playerSide isEqualTo west) then {
	_title = "Resetting Anti-Air System";
	_abort = "Resetting aborted due to moving too far from the device!"
};
_titleText ctrlSetText format["%2 (1%1)...","%",_title];
_progressBar progressSetPosition 0.01;
private _cP = 0.01;
if (playerSide isEqualTo civilian) then {
	_perkTier = ["civ_hackedAAs"] call EDEN_fnc_fetchStats;
	_cpRateChange = switch (_perkTier) do {
		case 1: {1.05};
		case 2: {1.10};
		case 3: {1.25};
		default {1};
	};
};
if (playerSide isEqualTo west) then {
	_perkTier = ["cop_repairedAAs"] call EDEN_fnc_fetchStats;
	_cpRateChange = switch (_perkTier) do {
		case 1: {1.025};
		case 2: {1.05};
		case 3: {1.075};
		case 4: {1.10};
		case 5: {1.125};
		default {1};
	};
};

["AinvPknlMstpSnonWnonDnon_medic_1",1.5] spawn EDEN_fnc_handleAnim;
for "_i" from 0 to 1 step 0 do {
	uisleep 0.3;
	_cP = _cP + (0.01 * _cpRateChange);
	_progressBar progressSetPosition _cP;
	_titleText ctrlSetText format["%3 (%1%2)...",round(_cP * 100),"%",_title];
	if (_cP >= 1 || !alive player) exitWith {};
	if (player distance _position > 4) exitWith {hint format["%1",_abort];eden_action_inUse = false;_close = true;};
	if (eden_interrupted) exitWith {eden_interrupted = false;titleText[localize "STR_NOTF_ActionCancel","PLAIN DOWN"];eden_action_inUse = false;_close = true;};
	if (player getVariable["restrained",false]) exitWith {eden_action_inUse = false;_close = true;};
};

5 cutText ["","PLAIN DOWN"];
[] spawn EDEN_fnc_handleAnim;
eden_action_inUse = false;
if (!alive player) exitWith {};
if (player distance _position > 4) exitWith {};
if (player getVariable ["restrained",false]) exitWith {};
if (_close) exitWith {};

if (playerSide isEqualTo civilian) then {
	if ((_object getVariable ["virus",""]) isEqualTo "CIV") exitWith {hint "System is already infected with a virus."};
	if (!(_object getVariable ["active",true]) && ((_object getVariable ["virus",""]) isEqualTo "")) exitWith {hint "System is already disabled."};
	if (((_object getVariable ["virus",""]) isEqualTo "") && {!([false,"hackingterminal",1] call EDEN_fnc_handleInv)}) exitWith {};

	if ((_object getVariable ["virus",""]) isEqualTo "") then {
		[[getPlayerUID player,profileName,"54",player],"EDENS_fnc_wantedAdd",false,false] spawn EDEN_fnc_MP;
	};
	["AA_hacked",1] spawn EDEN_fnc_statArrUp;
	[[_object,1],"EDENS_fnc_handleAntiAir",false,false] spawn EDEN_fnc_MP;
} else {
	if (_object isEqualTo islandAntiAir_1) exitWith {hint parseText "<t color='#ff2222'><t size='2.2'><t align='center'>SUCCESS!<br/><t color='#FFC966'><t align='center'><t size='1.2'>This federal events anti air system is now being repaired."};
	if (playerSide isEqualTo west && (_object getVariable ["active",true]) && {((_object getVariable ["virus",""]) isEqualTo "")}) exitWith {hint "System is in working condition."};
	if (playerSide isEqualTo west && !(_object getVariable ["active",true]) && {((_object getVariable ["virus",""]) isEqualTo "APD")}) exitWith {hint "System is repairing."};
	["AA_repaired",1] spawn EDEN_fnc_statArrUp;
	[[_object,2],"EDENS_fnc_handleAntiAir",false,false] spawn EDEN_fnc_MP;
};