//  File: fn_buyLicense.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Called when purchasing a license. May need to be revised.
if(isNil "oev_cash") then {oev_cash = 0; oev_cache_cash = oev_random_cash_val;};
if(isNil "oev_atmcash") then {oev_atmcash = 0; oev_cache_atmcash = oev_random_cash_val;};

private _action = false;
private _type = _this select 3;
private _price = [_type] call OEC_fnc_licensePrice;
private _license = [_type,0] call OEC_fnc_licenseType;

if((oev_cash + (oev_random_cash_val - 5000)) > oev_cache_cash || (oev_atmcash + (oev_random_cash_val - 5000)) > oev_cache_atmcash) exitWith {
	[["event","Hacked Cash"],["player",name player],["player_id",getPlayerUID player],["hackedcash",oev_cash - (oev_cache_cash - oev_random_cash_val)],["hackedbank",oev_atmcash - (oev_cache_atmcash - oev_random_cash_val)],["location",getPos player]] call OEC_fnc_logIt;
	[[profileName,format["Hacked Cash Detected! (Cash Hacked In = %1) (Bank Hacked In = %2)",oev_cash - (oev_cache_cash - oev_random_cash_val),oev_atmcash - (oev_cache_atmcash - oev_random_cash_val)]],"OEC_fnc_notifyAdmins",-2,false] spawn OEC_fnc_MP;
	[[1,player,[oev_cash - (oev_cache_cash - oev_random_cash_val),oev_atmcash - (oev_cache_atmcash - oev_random_cash_val)]],"OES_fnc_handleDisc",false,false] spawn OEC_fnc_MP;
	["HackedMoney",false,false] call compile PreProcessFileLineNumbers "\a3\functions_f\Misc\fn_endMission.sqf";
};

if(oev_atmcash < _price && oev_cash < _price) exitWith {hint format[localize "STR_NOTF_NE_1",[_price] call OEC_fnc_numberText,_license select 1];};

if(_type isEqualTo "vigilante") then {
	if !(O_stats_playtime_civ >= 120) exitWith {hint "You must have 2 hours on the server to buy a vigilante license!";};
	_action = [
		"Are you sure you want to buy the Vigilante License? If you have the Rebel or Workers Protection license(s) they will be revoked upon purchase.",
		"Confirmation",
		"Yes",
		"No"
	] call BIS_fnc_guiMessage;

	if (_action) then {
		license_civ_rebel = false;
		license_civ_wpl = false;
		player setVariable ["isVigi",true,true];
		if ((player getVariable ["vigilanteArrests",-1]) isEqualTo -1) then {
			player setVariable ["vigilanteArrests",0,true];
			[[3,player],"OES_fnc_vigiGetSetArrests",false,false] spawn OEC_fnc_MP;
		} else {
			oev_vigiarrests = player getVariable ["vigilanteArrests",0];
		};
		hint "If you had any of the following licenses: Rebel, Workers Protection. They are now revoked!";
		[2] call OEC_fnc_ClupdatePartial;
	};
};

if(_type isEqualTo "rebel") then {
	_action = [
		"Are you sure you want to buy the Rebel License? If you have the Vigilante or Workers Protection license(s) they will be revoked upon purchase.",
		"Confirmation",
		"Yes",
		"No"
	] call BIS_fnc_guiMessage;

	if (_action) then {
		if (license_civ_vigilante) then {
			player setVariable ["isVigi",false,true];
			player setVariable ["vigilanteArrests",0,true];
			[[3,player],"OES_fnc_vigiGetSetArrests",false,false] spawn OEC_fnc_MP;
		};
		license_civ_vigilante = false;
		license_civ_wpl = false;
		hint "If you had any of the following licenses: Vigilante, Workers Protection. They are now revoked!";
		[2] call OEC_fnc_ClupdatePartial;
	};
};

if(_type isEqualTo "wpl") then {
	_action = [
		"Are you sure you want to buy the Worker Protection License? If you have the Vigilante or Rebel license(s) they will be revoked upon purchase.",
		"Confirmation",
		"Yes",
		"No"
	] call BIS_fnc_guiMessage;

	if (_action) then {
		if (license_civ_vigilante) then {
			player setVariable ["isVigi",false,true];
			[[3,player],"OES_fnc_vigiGetSetArrests",false,false] spawn OEC_fnc_MP;
			player setVariable ["vigilanteArrests",0,true];
		};
		license_civ_vigilante = false;
		license_civ_rebel = false;
		hint "If you had the rebel or vigilante license it is now revoked!";
		[2] call OEC_fnc_ClupdatePartial;
	};
};

if(!_action && (_type in ["wpl","rebel","vigilante"])) exitWith {};


if(oev_cash >= _price) then {
	oev_cash = oev_cash - _price;
	oev_cache_cash = oev_cache_cash - _price;
}else{
	oev_atmcash = oev_atmcash - _price;
	oev_cache_atmcash = oev_cache_atmcash - _price;
};

titleText[format["You bought a %1 for $%2. Press ESC to sync!", _license select 1,[_price] call OEC_fnc_numberText],"PLAIN DOWN"];
missionNamespace setVariable[(_license select 0),true];

if (_action && (_type isEqualTo "vigilante") && !(oev_inCombat)) then {[] call OEC_fnc_vigiNotify;};
