//  File: fn_varHandle.sqf
//	Author: <PERSON> "Toni<PERSON>" Boardwine

//	Description: Master handler for getting a variables name, short name, etc.
private["_var","_mode"];
_var = param [0,"",[""]];
_mode = param [1,-1,[0]];
if(_var == "" || _mode == -1) exitWith {""};

switch (_mode) do {
	case 0:	{
		switch (_var) do {
			case "lethalinjector": {"life_inv_lethalinjector"};
			case "oilu": {"life_inv_oilu"};
			case "oilp": {"life_inv_oilp"};
			case "heroinu": {"life_inv_heroinu"};
			case "heroinp": {"life_inv_heroinp"};
			case "cannabis": {"life_inv_cannabis"};
			case "marijuana": {"life_inv_marijuana"};
			case "apple": {"life_inv_apple"};
			case "water": {"life_inv_water"};
			case "salema": {"life_inv_salema"};
			case "ornate": {"life_inv_ornate"};
			case "mackerel": {"life_inv_mackerel"};
			case "tuna": {"life_inv_tuna"};
			case "mullet": {"life_inv_mullet"};
			case "catshark": {"life_inv_catshark"};
			case "turtle": {"life_inv_turtle"};
			case "fishing": {"life_inv_fishingpoles"};
			case "coffee": {"life_inv_coffee"};
			case "turtlesoup": {"life_inv_turtlesoup"};
			case "donuts": {"life_inv_donuts"};
			case "fuelE": {"life_inv_fuelE"};
			case "fuelF": {"life_inv_fuelF"};
			case "money": {"eden_cash"};
			case "pickaxe": {"life_inv_pickaxe"};
			case "copperore": {"life_inv_copperore"};
			case "ironore": {"life_inv_ironore"};
			case "ironr": {"life_inv_ironr"};
			case "copperr": {"life_inv_copperr"};
			case "salt": {"life_inv_salt"};
			case "saltr": {"life_inv_saltr"};
			case "sand": {"life_inv_sand"};
			case "glass": {"life_inv_glass"};
			case "tbacon": {"life_inv_tbacon"};
			case "lockpick": {"life_inv_lockpick"};
			case "redgull": {"life_inv_redgull"};
			case "lollypop": {"life_inv_lollypop"};
			case "peach": {"life_inv_peach"};
			case "diamond": {"life_inv_diamond"};
			case "diamondc": {"life_inv_diamondr"};
			case "cocaine": {"life_inv_coke"};
			case "cocainep": {"life_inv_cokep"};
			case "spikeStrip": {"life_inv_spikeStrip"};
			case "cement": {"life_inv_cement"};
			case "rock": {"life_inv_rock"};
			case "goldbar": {"life_inv_goldbar"};
			case "moneybag": {"life_inv_moneybag"};
			case "blastingcharge": {"life_inv_blastingcharge"};
			case "boltcutter": {"life_inv_boltcutter"};
			case "fireaxe": {"life_inv_fireaxe"};
			case "defusekit": {"life_inv_defusekit"};
			case "storagesmall": {"life_inv_storagesmall"};
			case "storagebig": {"life_inv_storagebig"};
			case "frog": {"life_inv_frog"};
			case "frogp": {"life_inv_frogp"};
			case "crystalmeth": {"life_inv_crystalmeth"};
			case "methu": {"life_inv_methu"};
			case "phosphorous": {"life_inv_phosphorous"};
			case "ephedra": {"life_inv_ephedra"};
			case "lithium": {"life_inv_lithium"};
			case "moonshine": {"life_inv_moonshine"};
			case "rum": {"life_inv_rum"};
			case "mashu": {"life_inv_mashu"};
			case "corn": {"life_inv_corn"};
			case "sugar": {"life_inv_sugar"};
			case "yeast": {"life_inv_yeast"};
			case "platinum": {"life_inv_platinum"};
			case "platinumr": {"life_inv_platinumr"};
			case "silver": {"life_inv_silver"};
			case "silverr": {"life_inv_silverr"};
			case "beer": {"life_inv_beer"};
			case "ziptie": {"life_inv_ziptie"};
			case "cupcake": {"life_inv_cupcake"};
			case "pepsi": {"life_inv_pepsi"};
			case "burger": {"life_inv_burger"};
			case "mushroom": {"life_inv_mushroom"};
			case "mmushroom": {"life_inv_mmushroom"};
			case "mmushroomp": {"life_inv_mmushroomp"};
			case "gpstracker": {"life_inv_gpstracker"};
			case "egpstracker": {"life_inv_egpstracker"};
			case "gpsjammer": {"life_inv_gpsjammer"};
			case "ccocaine": {"life_inv_ccocaine"};
			case "kidney": {"life_inv_kidney"};
			case "scalpel": {"life_inv_scalpel"};
			case "barrier": {"life_inv_barrier"};
			case "speedbomb": {"life_inv_speedbomb"};
			case "foodDiv": {"life_inv_foodDiv"};
			case "legalDiv": {"life_inv_legalDiv"};
			case "illegalDiv": {"life_inv_illegalDiv"};
			case "fireworks": {"life_inv_fireworks"};
			case "heliTowHook": {"life_inv_heliTowHook"};
			case "hen_raw": {"life_inv_chickenRaw"};
			case "rooster_raw": {"life_inv_roosterRaw"};
			case "goat_raw": {"life_inv_goatRaw"};
			case "sheep_raw": {"life_inv_sheepRaw"};
			case "rabbit_raw": {"life_inv_rabbitRaw"};
			case "snake_raw": {"life_inv_snakeRaw"};
			case "hen_processed": {"life_inv_chicken"};
			case "rooster_processed": {"life_inv_rooster"};
			case "goat_processed": {"life_inv_goat"};
			case "sheep_processed": {"life_inv_sheep"};
			case "rabbit_processed": {"life_inv_rabbit"};
			case "snake_processed": {"life_inv_snake"};
			case "potato": {"life_inv_potato"};
			case "cream": {"life_inv_cream"};
			case "bloodbag": {"life_inv_bloodbag"};
			case "epiPen": {"life_inv_epiPen"};
			case "dopeShot": {"life_inv_dopeShot"};
			case "woodLog": {"life_inv_woodLog"};
			case "lumber": {"life_inv_lumber"};
			case "bananau": {"life_inv_bananau"};
			case "bananap": {"life_inv_bananap"};
			case "topaz": {"life_inv_topaz"};
			case "topazr": {"life_inv_topazr"};
			case "cocoau": {"life_inv_cocoau"};
			case "cocoap": {"life_inv_cocoap"};
			case "bananaSplit": {"life_inv_bananaSplit"};
			case "sugarp": {"life_inv_sugarp"};
			case "hackingterminal": {"life_inv_hackingterminal"};
			case "takeoverterminal": {"life_inv_takeoverterminal"};
			case "blindfold": {"life_inv_blindfold"};
			case "panicButton": {"life_inv_panicButton"};
			case "wplPanicButton": {"life_inv_wplPanicButton"};
			case "roadKit": {"life_inv_roadKit"};
			case "oilbarrel": {"life_inv_oilbarrel"};
			case "vehAmmo": {"life_inv_vehAmmo"};
			case "baitcar": {"life_inv_baitcar"};
			case "emerald": {"life_inv_emerald"};
			case "amethyst": {"life_inv_amethyst"};
			case "bpearl": {"life_inv_bpearl"};
			case "wpearl": {"life_inv_wpearl"};
			case "coin": {"life_inv_coin"};
			case "stire": {"life_inv_stire"};
			case "rubber": {"life_inv_rubber"};
			case "ltire": {"life_inv_ltire"};
			case "window": {"life_inv_window"};
			case "rglass": {"life_inv_rglass"};
			case "vdoor": {"life_inv_vdoor"};
			case "electronics": {"life_inv_electronics"};
			case "smetal": {"life_inv_smetal"};
			case "fibers": {"life_inv_fibers"};
			case "coal": {"life_inv_coal"};
			case "alumore": {"life_inv_alumore"};
			case "splating": {"life_inv_splating"};
			case "alumalloy": {"life_inv_alumalloy"};
			case "scrap": {"life_inv_scrap"};
			case "excavationtools": {"life_inv_excavationtools"};
			case "hash": {"life_inv_hash"};
			case "acid": {"life_inv_acid"};
			case "mushroomu": {"life_inv_mushroomu"};
			case "pheroin": {"life_inv_pheroin"};
			case "painkillers": {"life_inv_painkillers"};
			case "crack": {"life_inv_crack"};
			case "bcremote": {"life_inv_bcremote"};
			case "paintingSm": {"life_inv_paintingSm"};
			case "paintingLg": {"life_inv_paintingLg"};
			case "gokart": {"life_inv_gokart"};
		};
	};

	case 1:	{
		switch (_var) do {
			case "life_inv_lethalinjector": {"lethalinjector"};
			case "life_inv_rubber": {"rubber"};
			case "life_inv_alumore": {"alumore"};
			case "life_inv_coal": {"coal"};
			case "life_inv_fibers": {"fibers"};
			case "life_inv_stire": {"stire"};
			case "life_inv_ltire": {"ltire"};
			case "life_inv_window": {"window"};
			case "life_inv_rglass": {"rglass"};
			case "life_inv_vdoor": {"vdoor"};
			case "life_inv_electronics": {"electronics"};
			case "life_inv_smetal": {"smetal"};
			case "life_inv_splating": {"splating"};
			case "life_inv_alumalloy": {"alumalloy"};
			case "life_inv_hash": {"hash"};
			case "life_inv_acid": {"acid"};
			case "life_inv_mushroomu": {"mushroomu"};
			case "life_inv_pheroin": {"pheroin"};
			case "life_inv_painkillers": {"painkillers"};
			case "life_inv_crack": {"crack"};
			case "life_inv_oilu": {"oilu"};
			case "life_inv_oilp": {"oilp"};
			case "life_inv_heroinu": {"heroinu"};
			case "life_inv_heroinp": {"heroinp"};
			case "life_inv_cannabis": {"cannabis"};
			case "life_inv_marijuana": {"marijuana"};
			case "life_inv_apple": {"apple"};
			case "life_inv_water": {"water"};
			case "life_inv_salema": {"salema"};
			case "life_inv_ornate": {"ornate"};
			case "life_inv_mackerel": {"mackerel"};
			case "life_inv_tuna": {"tuna"};
			case "life_inv_mullet": {"mullet"};
			case "life_inv_catshark": {"catshark"};
			case "life_inv_turtle": {"turtle"};
			case "life_inv_fishingpoles": {"fishing"};
			case "life_inv_coffee": {"coffee"};
			case "life_inv_turtlesoup": {"turtlesoup"};
			case "life_inv_donuts": {"donuts"};
			case "life_inv_fuelE": {"fuelE"};
			case "life_inv_fuelF": {"fuelF"};
			case "eden_cash": {"money"};
			case "life_inv_pickaxe": {"pickaxe"};
			case "life_inv_copperore": {"copperore"};
			case "life_inv_ironore": {"ironore"};
			case "life_inv_ironr": {"ironr"};
			case "life_inv_copperr": {"copperr"};
			case "life_inv_sand": {"sand"};
			case "life_inv_salt": {"salt"};
			case "life_inv_glass": {"glass"};
			case "life_inv_redgull": {"redgull"};
			case "life_inv_lollypop": {"lollypop"};
			case "life_inv_lockpick": {"lockpick"};
			case "life_inv_tbacon": {"tbacon"};
			case "life_inv_peach": {"peach"};
			case "life_inv_diamond": {"diamond"};
			case "life_inv_diamondr": {"diamondc"};
			case "life_inv_saltr": {"saltr"};
			case "life_inv_coke": {"cocaine"};
			case "life_inv_cokep": {"cocainep"};
			case "life_inv_spikeStrip": {"spikeStrip"};
			case "life_inv_cement": {"cement"};
			case "life_inv_rock": {"rock"};
			case "life_inv_goldbar": {"goldbar"};
			case "life_inv_moneybag": {"moneybag"};
			case "life_inv_blastingcharge": {"blastingcharge"};
			case "life_inv_boltcutter": {"boltcutter"};
			case "life_inv_fireaxe": {"fireaxe"};
			case "life_inv_defusekit": {"defusekit"};
			case "life_inv_storagesmall": {"storagesmall"};
			case "life_inv_storagebig": {"storagebig"};
			case "life_inv_frog": {"frog"};
			case "life_inv_frogp": {"frogp"};
			case "life_inv_crystalmeth": {"crystalmeth"};
			case "life_inv_methu": {"methu"};
			case "life_inv_phosphorous": {"phosphorous"};
			case "life_inv_ephedra": {"ephedra"};
			case "life_inv_lithium": {"lithium"};
			case "life_inv_moonshine": {"moonshine"};
			case "life_inv_rum": {"rum"};
			case "life_inv_mashu": {"mashu"};
			case "life_inv_corn": {"corn"};
			case "life_inv_sugar": {"sugar"};
			case "life_inv_yeast": {"yeast"};
			case "life_inv_platinum": {"platinum"};
			case "life_inv_platinumr": {"platinumr"};
			case "life_inv_silver": {"silver"};
			case "life_inv_silverr": {"silverr"};
			case "life_inv_beer": {"beer"};
			case "life_inv_ziptie": {"ziptie"};
			case "life_inv_cupcake": {"cupcake"};
			case "life_inv_pepsi": {"pepsi"};
			case "life_inv_burger": {"burger"};
			case "life_inv_mushroom": {"mushroom"};
			case "life_inv_mmushroom": {"mmushroom"};
			case "life_inv_mmushroomp": {"mmushroomp"};
			case "life_inv_gpstracker": {"gpstracker"};
			case "life_inv_egpstracker": {"egpstracker"};
			case "life_inv_gpsjammer": {"gpsjammer"};
			case "life_inv_ccocaine": {"ccocaine"};
			case "life_inv_kidney": {"kidney"};
			case "life_inv_scalpel": {"scalpel"};
			case "life_inv_barrier": {"barrier"};
			case "life_inv_speedbomb": {"speedbomb"};
			case "life_inv_foodDiv": {"foodDiv"};
			case "life_inv_legalDiv": {"legalDiv"};
			case "life_inv_illegalDiv": {"illegalDiv"};
			case "life_inv_fireworks": {"fireworks"};
			case "life_inv_heliTowHook": {"heliTowHook"};
			case "life_inv_chickenRaw": {"hen_raw"};
			case "life_inv_roosterRaw": {"rooster_raw"};
			case "life_inv_goatRaw": {"goat_raw"};
			case "life_inv_sheepRaw": {"sheep_raw"};
			case "life_inv_rabbitRaw": {"rabbit_raw"};
			case "life_inv_snakeRaw": {"snake_raw"};
			case "life_inv_chicken": {"hen_processed"};
			case "life_inv_rooster": {"rooster_processed"};
			case "life_inv_goat": {"goat_processed"};
			case "life_inv_sheep": {"sheep_processed"};
			case "life_inv_rabbit": {"rabbit_processed"};
			case "life_inv_snake": {"snake_processed"};
			case "life_inv_potato": {"potato"};
			case "life_inv_cream": {"cream"};
			case "life_inv_bloodbag": {"bloodbag"};
			case "life_inv_epiPen": {"epiPen"};
			case "life_inv_dopeShot": {"dopeShot"};
			case "life_inv_woodLog": {"woodLog"};
			case "life_inv_lumber": {"lumber"};
			case "life_inv_bananau": {"bananau"};
			case "life_inv_bananap": {"bananap"};
			case "life_inv_topaz": {"topaz"};
			case "life_inv_topazr": {"topazr"};
			case "life_inv_cocoau": {"cocoau"};
			case "life_inv_cocoap": {"cocoap"};
			case "life_inv_bananaSplit": {"bananaSplit"};
			case "life_inv_sugarp": {"sugarp"};
			case "life_inv_hackingterminal": {"hackingterminal"};
			case "life_inv_takeoverterminal": {"takeoverterminal"};
			case "life_inv_blindfold": {"blindfold"};
			case "life_inv_panicButton": {"panicButton"};
			case "life_inv_wplPanicButton": {"wplPanicButton"};
			case "life_inv_roadKit": {"roadKit"};
			case "life_inv_oilbarrel": {"oilbarrel"};
			case "life_inv_vehAmmo": {"vehAmmo"};
			case "life_inv_baitcar": {"baitcar"};
			case "life_inv_emerald": {"emerald"};
			case "life_inv_amethyst": {"amethyst"};
			case "life_inv_scrap": {"scrap"};
			case "life_inv_coin": {"coin"};
			case "life_inv_wpearl": {"wpearl"};
			case "life_inv_bpearl": {"bpearl"};
			case "life_inv_excavationtools": {"excavationtools"};
			case "life_inv_bcremote": {"bcremote"};
			case "life_inv_paintingSm": {"paintingSm"};
			case "life_inv_paintingLg": {"paintingLg"};
			case "life_inv_gokart": {"gokart"}
		};
	};
};
